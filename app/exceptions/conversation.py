from constants.extracted_data import ConfirmedDataFields

from .base import ApplicationError


__all__ = ['ConversationDataInconsistencyError', 'ConfirmedDataReplacementError']


class ConversationDataInconsistencyError(ApplicationError):
    """Raised when conversation message data is invalid."""

    pass


class ConfirmedDataReplacementError(ApplicationError):
    """
    Exception raised when user is trying to replace confirmed fields (excluding engagement dates, as requested in TASK-2339979).
    """

    msg = 'Confirmed fields replacement detected in {source}: {fields_being_replaced}'

    def __init__(self, fields_being_replaced: list[ConfirmedDataFields], source: str):
        self.fields_being_replaced = fields_being_replaced
        self.formatted_fields = ','.join(field.value for field in fields_being_replaced)
        self.source = source
        self.formatted_msg = self.msg.format(fields_being_replaced=self.formatted_fields, source=self.source)
        super().__init__(self.formatted_msg)
