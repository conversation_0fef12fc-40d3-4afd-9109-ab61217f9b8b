from uuid import UUID

from constants.conflict import ConflictResolutionState
from exceptions import EntityNotFoundError
from repositories.conflict import ConflictRepository
from schemas import Conflict


__all__ = ['ConflictService']


class ConflictService:
    """Service for conflict resolving logic"""

    def __init__(
        self,
        conflict_repository: ConflictRepository,
    ):
        self.conflict_repository = conflict_repository

    async def has_unresolved_conflicts(self, conversation_id: UUID) -> bool:
        """
        Check if a conversation has any unresolved conflicts.

        Args:
            conversation_id: The ID of the conversation

        Returns:
            bool: True if there are unresolved conflicts, False otherwise
        """
        return await self.conflict_repository.has_unresolved_conflicts(conversation_id)

    async def get_conversation_conflicts(
        self,
        conversation_id: UUID,
        field: str | None = None,
        description: str | None = None,
        exclude_resolved: bool = False,
    ) -> list[Conflict]:
        """
        Get all conflicts for a specific conversation.

        Args:
            conversation_id: The ID of the conversation
            field: Optional field to filter conflicts
            description: Optional description to filter conflicts
            exclude_resolved: If True, only unresolved conflicts are returned

        Returns:
            list[QualConflict]: List of conflicts for the conversation
        """
        conflicts = await self.conflict_repository.get_conversation_conflicts(
            conversation_id=conversation_id, field=field, description=description, exclude_resolved=exclude_resolved
        )
        return [Conflict.model_validate(c) for c in conflicts]

    async def resolve_conflict(
        self,
        description: str,
        conversation_id: UUID,
        chosen_value: str,
    ) -> None:
        """
        Resolve a conflict by updating the chosen value.

        Args:
            description: Description of the conflict
            conversation_id: The ID of the conversation
            chosen_value: The value chosen to resolve the conflict

        Raises:
            EntityNotFoundError: If no conflicts match the description and conversation ID
        """
        conflicts = await self.get_conversation_conflicts(
            conversation_id=conversation_id, description=description, exclude_resolved=True
        )

        if not conflicts:
            raise EntityNotFoundError('Conflict', f'Description: {description}, Conversation ID: {conversation_id}')

        conflict = conflicts[0]
        await self.conflict_repository.update_chosen_value(conflict.id, chosen_value)

    async def get_conflict_resolution_state(self, conversation_id: UUID) -> ConflictResolutionState:
        """
        Check if all conflicts for a conversation have been resolved.

        Args:
            conversation_id: The ID of the conversation

        Returns:
            bool | None: True if all conflicts are resolved, False if some are unresolved, None if no conflicts exist
        """
        conflicts_count = await self.conflict_repository.check_conversation_conflicts(conversation_id)
        if conflicts_count is None:
            return ConflictResolutionState.NOT_DETECTED
        elif conflicts_count:
            return ConflictResolutionState.ALL_RESOLVED
        else:
            return ConflictResolutionState.NOT_RESOLVED
