import json
import logging

from openai.types.chat import (
    ChatCompletionMessageParam,
)

from config import settings
from constants.message import TextEditCommand
from constants.prompt import PromptTemplates
from repositories import OpenAIRepository
from schemas.text_edit import TextEditCommandValidator


__all__ = ['TextEditService']


logger = logging.getLogger(__name__)


class TextEditService:
    """Validates and extracts date information from user messages."""

    _PROMPT_COMMANDS = {
        TextEditCommand.EXPAND: PromptTemplates.text_edit_expand,
        TextEditCommand.REWRITE: PromptTemplates.text_edit_rewrite,
        TextEditCommand.SHORTEN: PromptTemplates.text_edit_shorten,
        TextEditCommand.PROMPT: PromptTemplates.text_edit_prompt,
    }

    def __init__(self, openai_repository: OpenAIRepository):
        self.openai_repository = openai_repository

    async def text_edit(
        self,
        command: TextEditCommand,
        data_to_analyze: dict,
        temperature: float = 0.0,
        model: str = settings.openai.model,
    ) -> str:
        """
        Edit the text using an LLM.

        Args:
            command: The command to use.
            full_text: The full text to edit.
            snippet: The snippet to edit.
            temperature: The temperature to use.
            model: The model to use.

        Returns:
            The edited text.
        """
        formatted_data_to_analyze = json.dumps(data_to_analyze)

        # Prepare the messages
        system_prompt = self._PROMPT_COMMANDS[command].SYSTEM
        user_prompt = self._PROMPT_COMMANDS[command].USER.format(
            json_input=formatted_data_to_analyze,
        )
        messages: list[ChatCompletionMessageParam] = [
            {'role': 'system', 'content': system_prompt},
            {'role': 'user', 'content': user_prompt},
        ]

        response = await self.openai_repository.generate_chat_completion(
            model=model,
            messages=messages,
            temperature=temperature,
            response_format=TextEditCommandValidator,
        )

        logger.info(f'Generated text edit completion: {response}')
        return str(response)
