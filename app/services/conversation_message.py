import logging
from typing import Sequence, cast
from uuid import UUID

from fastapi import UploadFile

from constants.message import MessageR<PERSON>, OptionType, PageType, TextEditCommand
from exceptions import EntityNotFoundError
from repositories import (
    ConversationMessageRepository,
    ConversationRepository,
    DocumentDbRepository,
    FieldRepository,
    OpenAIRepository,
    ProcessingMessageRepository,
)
from schemas import (
    BaseMessageSerializer,
    CombinedMessageSerializer,
    Command,
    LDMFCountryOption,
    MessageValidator,
    Option,
    UserMessageSerializer,
)
from services.client_industry import ClientIndustryDataService
from services.client_service import ClientServiceDataService
from services.command import CommandService
from services.conflicts import ConflictService
from services.date_validator import DateValidatorService
from services.document import DocumentService
from services.engagement_field_modification import EngagementFieldModificationService
from services.engagement_intent_processor import EngagementIntentProcessor
from services.engagement_location import EngagementLocationDataService
from services.extracted_data import ExtractedDataService
from services.intent_classifier import IntentClassifierService
from services.kx_dash import KXDashService
from services.message_handlers.engagement_description_handler import EngagementDescriptionMessageHandler
from services.message_handlers.engagement_details_handler import EngagementDetailsMessageHandler
from services.message_handlers.prompt_handler import PromptMessageHandler
from services.message_handlers.usage_and_team_details_handler import UsageAndTeamDetailsMessageHandler
from services.openai_client_name_validation import OpenAIClientNameValidationService
from services.option_handlers import (
    ClientNameOptionHandlerService,
    ConflictOptionHandlerService,
    DatesOptionHandlerService,
    KXDashTaskOptionHandlerService,
    LDMFCountryOptionHandlerService,
)
from services.system_message_generation import SystemMessageGenerationService
from services.translation import TranslationService


__all__ = ['ConversationMessageService']

logger = logging.getLogger(__name__)


class ConversationMessageService:
    """Service for conversation message-related business logic."""

    # Define the expected empty LDMF country option for clarity
    _EMPTY_LDMF_COUNTRY_OPTION = [LDMFCountryOption(type=OptionType.LDMF_COUNTRY, ldmf_country='')]

    def __init__(
        self,
        conflict_service: ConflictService,
        conversation_message_repository: ConversationMessageRepository,
        conversation_repository: ConversationRepository,
        document_service: DocumentService,
        document_db_repository: DocumentDbRepository,
        processing_message_repository: ProcessingMessageRepository,
        kx_dash_service: KXDashService,
        intent_classifier_service: IntentClassifierService,
        translation_service: TranslationService,
        extracted_data_service: ExtractedDataService,
        date_validator_service: DateValidatorService,
        system_message_generation_service: SystemMessageGenerationService,
        client_industry_service: ClientIndustryDataService,
        client_service_service: ClientServiceDataService,
        engagement_location_service: EngagementLocationDataService,
        # Option handlers
        client_name_option_handler: ClientNameOptionHandlerService,
        conflict_option_handler: ConflictOptionHandlerService,
        ldmf_country_option_handler: LDMFCountryOptionHandlerService,
        dates_option_handler: DatesOptionHandlerService,
        kx_dash_task_option_handler: KXDashTaskOptionHandlerService,
        command_service: CommandService,
        # Engagement handlers
        engagement_intent_processor: EngagementIntentProcessor,
        engagement_field_modification_service: EngagementFieldModificationService,
        openai_repository: OpenAIRepository,
        field_repository: FieldRepository,
        openai_client_name_validation_service: OpenAIClientNameValidationService,
    ):
        self.conflict_service = conflict_service
        self.conversation_message_repository = conversation_message_repository
        self.conversation_repository = conversation_repository
        self.document_service = document_service
        self.document_db_repository = document_db_repository
        self.processing_message_repository = processing_message_repository
        self.kx_dash_service = kx_dash_service
        self.translation_service = translation_service
        self.intent_classifier_service = intent_classifier_service
        self.extracted_data_service = extracted_data_service
        self.date_validator_service = date_validator_service
        self.system_message_service = system_message_generation_service
        self.client_industry_service = client_industry_service
        self.client_service_service = client_service_service
        self.engagement_location_service = engagement_location_service
        self.openai_repository = openai_repository
        self.field_repository = field_repository

        # Option handlers
        self.client_name_option_handler = client_name_option_handler
        self.conflict_option_handler = conflict_option_handler
        self.ldmf_country_option_handler = ldmf_country_option_handler
        self.dates_option_handler = dates_option_handler
        self.kx_dash_task_option_handler = kx_dash_task_option_handler

        # Command handlers
        self.command_service = command_service
        self.openai_client_name_validation_service = openai_client_name_validation_service

        self._message_handlers = {
            PageType.PROMPT: PromptMessageHandler(
                conflict_service=self.conflict_service,
                conversation_message_repository=self.conversation_message_repository,
                conversation_repository=self.conversation_repository,
                document_service=self.document_service,
                document_db_repository=self.document_db_repository,
                processing_message_repository=self.processing_message_repository,
                kx_dash_service=self.kx_dash_service,
                translation_service=self.translation_service,
                extracted_data_service=self.extracted_data_service,
                date_validator_service=self.date_validator_service,
                system_message_generation_service=self.system_message_service,
                client_industry_service=self.client_industry_service,
                client_service_service=self.client_service_service,
                engagement_location_service=self.engagement_location_service,
                openai_repository=self.openai_repository,
                client_name_option_handler=self.client_name_option_handler,
                conflict_option_handler=self.conflict_option_handler,
                ldmf_country_option_handler=self.ldmf_country_option_handler,
                dates_option_handler=self.dates_option_handler,
                kx_dash_task_option_handler=self.kx_dash_task_option_handler,
                openai_client_name_validation_service=self.openai_client_name_validation_service,
            ),
            PageType.ENGAGEMENT_DESCRIPTION: EngagementDescriptionMessageHandler(
                conversation_message_repository=self.conversation_message_repository,
                engagement_intent_processor=engagement_intent_processor,
                engagement_field_modification_service=engagement_field_modification_service,
                openai_repository=self.openai_repository,
                command_service=self.command_service,
                field_repository=self.field_repository,
                conversation_repository=self.conversation_repository,
                extracted_data_service=self.extracted_data_service,
            ),
            PageType.ENGAGEMENT_DETAILS: EngagementDetailsMessageHandler(
                conversation_message_repository=self.conversation_message_repository,
            ),
            PageType.USAGE_AND_TEAM_DETAILS: UsageAndTeamDetailsMessageHandler(
                conversation_message_repository=self.conversation_message_repository,
            ),
        }

    async def create(
        self,
        conversation_id: UUID,
        content: str,
        selected_option: Option | None,
        command: Command | None,
        files: list[UploadFile] | None,
        token: str,
        page_type: PageType,
    ) -> CombinedMessageSerializer:
        await self._disable_undoable_messages(conversation_id)

        handler = self._message_handlers.get(page_type)
        if not handler:
            raise NotImplementedError(f'Handler for page type {page_type} is not implemented')

        return await handler.handle(
            conversation_id=conversation_id,
            content=content,
            selected_option=selected_option,
            command=command,
            files=files,
            token=token,
        )

    async def create_message(self, message_data: MessageValidator) -> BaseMessageSerializer:
        """
        Create a new conversation message.

        Args:
            message_data: Data for creating the message

        Returns:
            Response with the created message data

        Raises:
            EntityNotFoundError: If the conversation doesn't exist
            DatabaseException: If there's an error creating the message
        """
        logger.debug('Creating new message for conversation ID: %s', message_data.conversation_id)
        try:
            return await self.conversation_message_repository.create(message_data)

        except Exception as e:  # pragma: no cover
            logger.error('Error creating message: %s', e)
            raise

    async def get(self, public_id: UUID) -> BaseMessageSerializer:
        """
        Get a message by its ID.

        Args:
            public_id: The ID of the message

        Returns:
            The message response

        Raises:
            EntityNotFoundError: If the message doesn't exist
            DatabaseException: If there's an error retrieving the message
        """
        try:
            logger.debug('Retrieving message with ID: %s', public_id)
            return await self.conversation_message_repository.get(public_id)

        except Exception as e:  # pragma: no cover
            logger.error('Error retrieving message: %s', e)
            raise

    async def list(self, public_id: UUID, page_type: PageType | None = None) -> Sequence[BaseMessageSerializer]:
        """
        Get all messages for a specific conversation.

        Args:
            public_id: The ID of the conversation
            page_type: Optional page type to filter messages

        Returns:
            Sequence of messages for the conversation

        Raises:
            EntityNotFoundError: If the conversation doesn't exist
            DatabaseException: If there's an error retrieving the messages
        """
        if not await self.conversation_repository.exists(public_id):
            raise EntityNotFoundError('Conversation', str(public_id))

        try:
            logger.debug('Retrieving all messages for conversation with ID: %s', public_id)
            messages = await self.conversation_message_repository.list(public_id, page_type=page_type)

            if page_type and page_type == PageType.ENGAGEMENT_DESCRIPTION:
                # Filter out messages pairs with user STORE command
                return [
                    messages[i + j]
                    for i in range(0, len(messages), 2)
                    for j in (0, 1)
                    if not (message := cast(UserMessageSerializer, messages[i])).command
                    or message.command.command != TextEditCommand.STORE
                ]
            else:
                return messages

        except Exception as e:  # pragma: no cover
            logger.error('Error retrieving messages for conversation %s: %s', public_id, e)
            raise

    async def get_last(self, public_id: UUID, token: str) -> BaseMessageSerializer:
        """
        Get the last message for a specific conversation.

        Args:
            public_id: The ID of the conversation
            token: The authentication token

        Returns:
            The last message for the conversation

        Raises:
            EntityNotFoundError: If the conversation doesn't exist
            DatabaseException: If there's an error retrieving the last message
        """
        try:
            logger.debug('Retrieving last message for conversation with ID: %s', public_id)
            last_message = await self.conversation_message_repository.get_last(public_id)

            if last_message.role != MessageRole.USER:
                return last_message

            handler = self._message_handlers.get(PageType.PROMPT)
            if not handler or not isinstance(handler, PromptMessageHandler):
                raise NotImplementedError(f'Handler for page type {PageType.PROMPT} is not implemented correctly')

            return await handler.generate_response_for_user_message(public_id, last_message, token)

        except Exception as e:  # pragma: no cover
            logger.error('Error retrieving last message for conversation %s: %s', public_id, e)
            raise

    async def delete_many(self, conversation_id: UUID) -> None:
        """
        Delete all messages for a specific conversation.

        Args:
            conversation_id: The ID of the conversation

        Returns:
            None

        Raises:
            DatabaseException: If there's an error deleting the messages
        """
        try:
            logger.debug('Deleting all messages for conversation with ID: %s', conversation_id)
            await self.conversation_message_repository.delete_many(conversation_id)

        except Exception as e:  # pragma: no cover
            logger.error('Error deleting messages for conversation %s: %s', conversation_id, e)
            raise

    async def update_message_fields(self, message_id: UUID, fields_to_update: dict) -> None:
        """
        Update specific fields of a conversation message.

        Args:
            message_id: The ID of the message to update.
            fields_to_update: A dictionary where keys are field names and values are the new values.
        """
        try:
            logger.debug('Updating message %s with fields: %s', message_id, fields_to_update)
            await self.conversation_message_repository.update_fields(message_id, fields_to_update)
        except Exception as e:
            logger.error('Error updating message %s: %s', message_id, e)
            raise

    async def get_owner_id(self, message_id: UUID) -> UUID | None:
        """
        Get an owner ID for the message.

        Args:
            message_id: The ID of the message

        Returns:
            The user ID if the message exists, None otherwise
        """
        try:
            logger.debug('Retrieving an owner ID for the message: %s', message_id)
            return await self.conversation_message_repository.get_owner_id(message_id)

        except Exception:  # pragma: no cover
            logger.exception('Failed to retrieve message owner ID')
            raise

    async def _disable_undoable_messages(self, conversation_id: UUID) -> None:
        """
        Disable any active undoable messages for a conversation.
        """
        try:
            logger.debug('Disabling undoable messages for conversation %s', conversation_id)
            await self.conversation_message_repository.disable_undoable_flags(conversation_id)
        except Exception as e:
            logger.error('Error disabling undoable messages for conversation %s: %s', conversation_id, e)
            raise
