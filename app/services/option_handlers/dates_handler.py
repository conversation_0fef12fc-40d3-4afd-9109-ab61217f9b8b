import logging
from uuid import UUID

from constants.extracted_data import ConfirmedData<PERSON>ields, ConversationState
from constants.message import MessageR<PERSON>, MessageType, SystemReplyType
from schemas import DatePickerOption, MessageValidator
from services.extracted_data import ExtractedDataService

from .base import BaseOptionHandler


__all__ = ['DatesOptionHandlerService']


logger = logging.getLogger(__name__)


class DatesOptionHandlerService(BaseOptionHandler[DatePickerOption]):
    """Service for handling dates option selections."""

    def __init__(self, extracted_data_service: ExtractedDataService):
        self.extracted_data_service = extracted_data_service

    async def handle(
        self,
        selected_option: DatePickerOption,
        conversation_id: UUID,
        token: str | None = None,
    ) -> MessageValidator:
        """
        Handle dates selection from options.

        Args:
            selected_option: The selected dates option
            conversation_id: The conversation ID
            token: Optional user token (not used for dates selection)

        Returns:
            MessageValidator: System message confirming the selection

        Raises:
            EntityNotFoundError: If the conversation doesn't exist
        """
        try:
            logger.debug('Handling dates selection: %s for conversation: %s', selected_option, conversation_id)
            start_date = selected_option.start_date.isoformat() if selected_option.start_date else None
            end_date = selected_option.end_date.isoformat() if selected_option.end_date else None

            # Create confirmation message
            reply_type = SystemReplyType.DATES_CONFIRMED
            confirmation_message = reply_type.message_text
            options = []

            if start_date and end_date:
                await self.extracted_data_service.update_confirmed_data(
                    conversation_id=conversation_id,
                    field_name=ConfirmedDataFields.DATE_INTERVALS.value,
                    field_value=(start_date, end_date),
                    state=ConversationState.COLLECTING_OBJECTIVE,  # Move to next state
                )
            else:
                await self.extracted_data_service.update_confirmed_data(
                    conversation_id=conversation_id,
                    field_name=ConfirmedDataFields.DATE_INTERVALS.value,
                    field_value=(start_date, end_date),
                    state=ConversationState.COLLECTING_DATES,  # Stay in the same state
                )

                reply_type = SystemReplyType.DATES_ONE_DATE
                confirmation_message = reply_type.message_text
                options = [selected_option]

            return MessageValidator(
                conversation_id=conversation_id,
                role=MessageRole.SYSTEM,
                type=MessageType.TEXT,
                content=confirmation_message,
                selected_option=selected_option,
                options=options,
                system_reply_type=reply_type,
            )

        except Exception as e:
            logger.error('Error handling dates selection: %s', e)
            raise
