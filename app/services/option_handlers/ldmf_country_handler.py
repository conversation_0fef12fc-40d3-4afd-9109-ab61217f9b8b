import logging
from uuid import UUID

from constants.extracted_data import ConfirmedData<PERSON>ields, ConversationState
from constants.message import MessageR<PERSON>, MessageType, SystemReplyType
from schemas import LDM<PERSON>ountryOption, MessageValidator
from services.extracted_data import ExtractedDataService

from .base import BaseOptionHandler


__all__ = ['LDMFCountryOptionHandlerService']


logger = logging.getLogger(__name__)


class LDMFCountryOptionHandlerService(BaseOptionHandler[LDMFCountryOption]):
    """Service for handling LDMF country option selections."""

    def __init__(self, extracted_data_service: ExtractedDataService):
        self.extracted_data_service = extracted_data_service

    async def handle(
        self,
        selected_option: LDMFCountryOption,
        conversation_id: UUID,
        token: str | None = None,
    ) -> MessageValidator:
        """
        Handle LDMF country selection from options.

        Args:
            selected_option: The selected LDMF country option
            conversation_id: The conversation ID
            token: Optional user token (not used for country selection)

        Returns:
            MessageValidator: System message confirming the selection

        Raises:
            EntityNotFoundError: If the conversation doesn't exist
        """
        try:
            logger.debug('Handling selection %s for conversation %s', selected_option.ldmf_country, conversation_id)

            # Update confirmed data with the selected country
            await self.extracted_data_service.update_confirmed_data(
                conversation_id=conversation_id,
                field_name=str(ConfirmedDataFields.LDMF_COUNTRY),
                field_value=selected_option.ldmf_country,
                state=ConversationState.COLLECTING_DATES,
            )

            # Create confirmation message
            reply_type = SystemReplyType.LDMF_COUNTRY_CONFIRMED
            confirmation_message = reply_type.message_text.format(ldmf_country=selected_option.ldmf_country)
            logger.info(
                '%s system_reply_type detected in %s',
                reply_type,
                'LDMFCountryOptionHandlerService.handle',
            )
            return MessageValidator(
                conversation_id=conversation_id,
                role=MessageRole.SYSTEM,
                type=MessageType.TEXT,
                content=confirmation_message,
                selected_option=selected_option,
                system_reply_type=reply_type,
            )

        except Exception as e:
            logger.error('Error handling country selection: %s', e)
            raise
