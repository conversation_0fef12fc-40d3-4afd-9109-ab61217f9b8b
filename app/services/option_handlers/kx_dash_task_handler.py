import logging
from uuid import <PERSON><PERSON><PERSON>

from schemas import K<PERSON><PERSON><PERSON><PERSON><PERSON>Option, MessageValidator
from services.kx_dash import KXDashService

from .base import BaseOptionHandler


__all__ = ['KXDashTaskOptionHandlerService']


logger = logging.getLogger(__name__)


class KXDashTaskOptionHandlerService(BaseOptionHandler[KXDashTaskOption]):
    """Service for handling KX Dash task option selections."""

    def __init__(self, kx_dash_service: KXDashService):
        self.kx_dash_service = kx_dash_service

    async def handle(
        self,
        selected_option: KXDashTaskOption,
        conversation_id: UUID,
        token: str | None = None,
    ) -> MessageValidator:
        """
        Handle KX Dash task selection from options.

        Args:
            selected_option: The selected KX Dash task option
            conversation_id: The conversation ID
            token: The user's token

        Returns:
            MessageValidator: System message confirming the selection

        Raises:
            EntityNotFoundError: If the conversation doesn't exist
        """
        if token is None:
            raise ValueError('Token is required for KX Dash task handling')

        try:
            logger.debug(
                'Handling KX Dash task selection: %s for conversation: %s', selected_option.activity_id, conversation_id
            )

            # Delegate to the existing KXDashService.on_select method
            return await self.kx_dash_service.on_select(
                selected_option=selected_option,
                conversation_id=conversation_id,
                token=token,
            )

        except Exception as e:
            logger.error('Error handling KX Dash task selection: %s', e)
            raise
