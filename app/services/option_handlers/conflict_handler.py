import asyncio
import json
import logging
from uuid import UUID

from openai.types.chat import (
    ChatCompletionSystemMessageParam,
    ChatCompletionUserMessageParam,
)

from config import settings
from constants.conflict import ConflictField
from constants.extracted_data import ConversationState
from constants.message import (
    THANX_FOR_INFORMATION,
    MessageRole,
    MessageType,
    OptionType,
    SuggestedUserPrompt,
    SystemReplyType,
)
from constants.prompt import prompt_templates
from exceptions import EntityNotFoundError
from repositories import ConversationRepository
from repositories.openai import OpenAIRepository
from schemas import ConfirmedData, ConflictResolve, LLMFieldConflictFinalized, MessageValidator
from services.conflicts import ConflictService

from .base import BaseOptionHandler


__all__ = ['ConflictOptionHandlerService']


logger = logging.getLogger(__name__)


class ConflictOptionHandlerService(BaseOptionHandler[ConflictResolve]):
    """Service for handling conflict option selections."""

    CONFLICT_SYSTEM_MESSAGE_REPLY_MAPPING = {
        'objective_scope': SystemReplyType.EXTRACTED_DATA_OBJECTIVE_SCOPE_MISSING,
        'outcomes': SystemReplyType.EXTRACTED_DATA_OUTCOMES_MISSING,
    }
    TEMPERATURE: float = settings.openai.default_temperature
    MAX_COMPLETION_TOKENS: int = settings.openai.max_completion_tokens

    def __init__(
        self,
        openai_repository: OpenAIRepository,
        conflict_service: ConflictService,
        conversation_repository: ConversationRepository,
    ):
        self.openai_repository = openai_repository
        self.conversation_repository = conversation_repository
        self.conflict_service = conflict_service

    async def handle(
        self,
        selected_option: ConflictResolve,
        conversation_id: UUID,
        token: str | None = None,
    ) -> MessageValidator:
        logger.debug('Handling conflicts resolution: %s for conversation: %s', selected_option.title, conversation_id)

        conflicts = await self.conflict_service.get_conversation_conflicts(
            conversation_id=conversation_id,
            description=selected_option.title,
            exclude_resolved=True,
        )
        if not conflicts:
            logger.error('Conflict title: %s not found for conversation_id: %s', selected_option.title, conversation_id)
            raise EntityNotFoundError('Conflict', selected_option.title)

        await self.conflict_service.resolve_conflict(
            description=selected_option.title,
            conversation_id=conversation_id,
            chosen_value=selected_option.conflict_item,
        )

        unresolved = await self.conflict_service.get_conversation_conflicts(
            conversation_id=conversation_id,
            exclude_resolved=True,
        )
        if unresolved:
            return self._build_conflict_response(conversation_id, unresolved)

        conversation, confirmed_data = await asyncio.gather(
            self.conversation_repository.get(conversation_id),
            self.conversation_repository.get_confirmed_data(conversation_id),
        )
        if not conversation or not confirmed_data:
            raise EntityNotFoundError('Conversation', conversation_id)

        update_payload = await self._finalize_fields(conversation_id, confirmed_data)

        # merge confirmed data with updates
        confirmed_data_dict = confirmed_data.model_dump()
        confirmed_data_dict.update(update_payload)
        state = ConversationState(conversation.State)
        reply_type = SystemReplyType.CONFIRMED_FIELDS_READY
        suggested_prompts = [SuggestedUserPrompt.NO_CREATE_MY_QUAL.value]
        if 'outcomes' in update_payload and not update_payload['outcomes']:
            state = ConversationState.COLLECTING_OUTCOMES
            reply_type = SystemReplyType.NEED_INFO_OUTCOMES
            suggested_prompts = []
        if 'objective_and_scope' in update_payload and not update_payload['objective_and_scope']:
            state = ConversationState.COLLECTING_OBJECTIVE
            reply_type = SystemReplyType.NEED_INFO_OBJECTIVE_SCOPE
            suggested_prompts = []

        await self.conversation_repository.update_confirmed_data_and_state(
            public_id=conversation_id,
            confirmed_data=ConfirmedData(**confirmed_data_dict),
            state=state,
            no_check=True,
        )

        return MessageValidator(
            conversation_id=conversation_id,
            role=MessageRole.SYSTEM,
            type=MessageType.TEXT,
            content=f'{THANX_FOR_INFORMATION} {reply_type.message_text}',
            suggested_prompts=suggested_prompts,
            system_reply_type=reply_type,
        )

    def _build_conflict_response(self, conversation_id: UUID, unresolved: list) -> MessageValidator:
        next_conflict = unresolved[0]
        resolve_options = next_conflict.conflicting_values
        resolve_options.append('None of the above')
        options = [
            ConflictResolve(
                title=next_conflict.description,
                type=OptionType.CONFLICT,
                conflict_item=value,
            )
            for value in resolve_options
        ]

        reply_type = SystemReplyType.CONFLICT_DETECTED
        return MessageValidator(
            conversation_id=conversation_id,
            role=MessageRole.SYSTEM,
            type=MessageType.TEXT,
            content=reply_type.message_text.format(counter=len(unresolved)),
            system_reply_type=reply_type,
            options=options,
        )

    async def _finalize_fields(self, conversation_id: UUID, confirmed_data: ConfirmedData) -> dict:
        """Summarize and finalize objectives/outcomes with LLM help."""
        update_payload = {}

        # collect conflicts concurrently
        objective_conflicts, outcome_conflicts = await asyncio.gather(
            self.conflict_service.get_conversation_conflicts(conversation_id, field=ConflictField.OBJECTIVES.value),
            self.conflict_service.get_conversation_conflicts(conversation_id, field=ConflictField.OUTCOMES.value),
        )

        async def summarize_if_needed(conflicts, field_value: str) -> str | None:
            if not conflicts:
                return None
            false_statements = []
            for conflict in conflicts:
                false_set = set(conflict.conflicting_values)
                if conflict.chosen_value:
                    false_set.discard(conflict.chosen_value)
                false_statements.extend(false_set)
            summarized_field = await self._summarize_field(field_value, false_statements)
            return summarized_field.sanitized_field.strip()

        summarized_objectives, summarized_outcome = await asyncio.gather(
            summarize_if_needed(objective_conflicts, confirmed_data.objective_and_scope),  # type: ignore
            summarize_if_needed(outcome_conflicts, confirmed_data.outcomes),  # type: ignore
        )

        if summarized_objectives is not None:
            update_payload['objective_and_scope'] = summarized_objectives
        if summarized_outcome is not None:
            update_payload['outcomes'] = summarized_outcome

        return update_payload

    async def _summarize_field(self, field_content: str, false_statements: list[str]) -> LLMFieldConflictFinalized:
        system_message = self._get_system_message(prompt_templates.summarize_conflicted_field.SYSTEM)
        user_message = self._get_user_message(
            prompt_templates.summarize_conflicted_field.USER.format(
                obj_str=json.dumps({'text': field_content, 'falsehoods_list': false_statements}),
            )
        )

        response = await self.openai_repository.generate_chat_completion(
            messages=[system_message, user_message],
            temperature=self.TEMPERATURE,
            response_format=LLMFieldConflictFinalized,
        )
        if not isinstance(response, LLMFieldConflictFinalized):
            raise RuntimeError(
                f'Invalid response from model, expected {LLMFieldConflictFinalized}, got {type(response)}'
            )
        return response

    @staticmethod
    def _get_system_message(message: str) -> ChatCompletionSystemMessageParam:
        return {'role': 'system', 'content': message}

    @staticmethod
    def _get_user_message(message: str) -> ChatCompletionUserMessageParam:
        return {'role': 'user', 'content': message}
