import logging
from typing import Any, Dict
from uuid import UUID

from openai.types.chat import Cha<PERSON><PERSON><PERSON>pletionMessageParam

from config import settings
from constants.engagement import ENGAGEMENT_SUMMARY_MAX_TOKENS, ONE_LINE_DESCRIPTION_MAX_TOKENS, engagement_templates
from constants.extracted_data import Tense
from constants.message import MessageR<PERSON>, MessageType, QualFieldName, SystemReplyType, TextEditCommand
from constants.prompt import SummaryPrompts, prompt_templates
from repositories import ConversationRepository, FieldRepository, OpenAIRepository
from schemas import Command, MessageValidator, QualFields, SumaryResponse, TextField
from services.extracted_data import ExtractedDataService
from services.text_edit import TextEditService


__all__ = ['CommandService']


logger = logging.getLogger(__name__)


class CommandService:
    """Service for handling command selections."""

    _REGENERATE_MAP: Dict[QualFieldName, Dict[str, Any]] = {
        QualFieldName.ENGAGEMENT_SUMMARY: {
            'system_prompt': prompt_templates.extract_engagement_summary,
            'user_prompt': prompt_templates.extract_engagement_summary,
            'max_tokens': ENGAGEMENT_SUMMARY_MAX_TOKENS,
            'response_format': SumaryResponse,
        },
        QualFieldName.ONE_LINE_DESCRIPTION: {
            'system_prompt': prompt_templates.extract_one_line_summary,
            'user_prompt': prompt_templates.extract_one_line_summary,
            'max_tokens': ONE_LINE_DESCRIPTION_MAX_TOKENS,
            'response_format': SumaryResponse,
        },
    }

    _RESULT_LENGTH_MAP = {
        QualFieldName.ENGAGEMENT_SUMMARY: 750,
        QualFieldName.ONE_LINE_DESCRIPTION: 150,
    }

    def __init__(
        self,
        field_repository: FieldRepository,
        text_edit_service: TextEditService,
        openai_repository: OpenAIRepository,
        conversation_repository: ConversationRepository,
        extracted_data_service: ExtractedDataService,
    ):
        self.field_repository = field_repository
        self.text_edit_service = text_edit_service
        self.openai_repository = openai_repository
        self.conversation_repository = conversation_repository
        self.extracted_data_service = extracted_data_service

    async def _handle_client_name_replacement(self, conversation_id: UUID) -> MessageValidator:
        """
        Replace the client name in all relevant fields.
        """
        confirmed_data = await self.conversation_repository.get_confirmed_data(conversation_id)
        client_name = confirmed_data.client_name
        if not client_name:
            return MessageValidator(
                conversation_id=conversation_id,
                role=MessageRole.SYSTEM,
                type=MessageType.ERROR,
                content='Client name not found to perform replacement.',
                system_reply_type=SystemReplyType.ENGAGEMENT_DETAILS_FIELD_ERROR,
            )

        fields_to_update = [
            QualFieldName.BUSINESS_ISSUES,
            QualFieldName.SCOPE_APPROACH,
            QualFieldName.VALUE_DELIVERED,
            QualFieldName.ENGAGEMENT_SUMMARY,
            QualFieldName.ONE_LINE_DESCRIPTION,
        ]

        qual_fields = {}
        modified_fields = []
        for field_name in fields_to_update:
            latest_field = await self.field_repository.get_latest_value(conversation_id, field_name)
            if latest_field and latest_field.FieldValue is not None:
                original_text = str(latest_field.FieldValue)
                updated_text = original_text.replace(client_name, 'the client')

                if original_text != updated_text:
                    await self.field_repository.create(
                        conversation_id=conversation_id,
                        field_name=field_name,
                        field_value=updated_text,
                        formatted_field_value=updated_text,
                    )
                    modified_fields.append(field_name)
                qual_fields[field_name.value] = TextField(context=updated_text, snippet='')

        return MessageValidator(
            conversation_id=conversation_id,
            role=MessageRole.SYSTEM,
            type=MessageType.TEXT,
            content=SystemReplyType.CLIENT_NAME_REPLACED.message_text.format(client_name=client_name),
            system_reply_type=SystemReplyType.CLIENT_NAME_REPLACED,
            qual_fields=QualFields.model_validate(qual_fields),
            is_undoable=False,
        )

    async def process_command(
        self,
        command: Command,
        content: str,
        conversation_id: UUID,
    ) -> MessageValidator:
        """
        Handle the selection of command.

        Args:
            command: The command to handle
            conversation_id: The conversation ID
            conversation_message_id: The conversation message ID

        Returns:
            The message data with the new full text
        """
        if command.command == TextEditCommand.REPLACE_CLIENT_NAME_CONFIRM:
            return await self._handle_client_name_replacement(conversation_id)
        if command.command == TextEditCommand.UNDO:
            return await self._undo_field_change(command, conversation_id)

        await self.field_repository.create(
            conversation_id=conversation_id,
            field_name=command.field_name,
            field_value=command.context,
            formatted_field_value=command.formatted_context,
        )
        text_edit_command = TextEditCommand(command.command)

        if text_edit_command != TextEditCommand.STORE:
            # Only pass content for PROMPT commands that need user context
            result_length = self._RESULT_LENGTH_MAP.get(command.field_name, None)
            data_to_analyze = self._generate_data_to_analyze(
                command.context,
                command.snippet,
                result_length,
                content if text_edit_command == TextEditCommand.PROMPT else None,
            )
            new_snippet = await self.text_edit_service.text_edit(
                command=text_edit_command,
                data_to_analyze=data_to_analyze,
                temperature=settings.openai.default_temperature,
                model=settings.openai.model,
            )
            command.snippet = new_snippet
            command.context = (
                data_to_analyze['text_before_snippet'] + new_snippet + data_to_analyze['text_after_snippet']
            )

        qual_fields = await self._regenerate_summaries_if_needed(command, conversation_id)
        qual_fields[command.field_name.value] = TextField(context=command.context, snippet=command.snippet)

        message_data = MessageValidator(
            conversation_id=conversation_id,
            role=MessageRole.SYSTEM,
            type=MessageType.TEXT,
            content=engagement_templates.general.edited_text_field_reply,
            selected_option=None,
            system_reply_type=SystemReplyType.FIELD_SAVED,
            options=[],
            command=command,
            qual_fields=QualFields.model_validate(qual_fields),
            is_undoable=text_edit_command != TextEditCommand.STORE,
        )

        return message_data

    async def _undo_field_change(self, command: Command, conversation_id: UUID) -> MessageValidator:
        """
        Undo the last change to a field.
        """
        # Handle chat undo with intelligent batch logic
        if command.field_name == QualFieldName.CHAT:
            fields_to_revert = await self._determine_chat_undo_fields(conversation_id)
        else:
            fields_to_revert = [command.field_name]
            if command.field_name.is_regenerator:
                fields_to_revert.extend([QualFieldName.ENGAGEMENT_SUMMARY, QualFieldName.ONE_LINE_DESCRIPTION])

        qual_fields = {}
        extracted_data = await self.extracted_data_service.aggregate_data(conversation_id)

        for field_name in fields_to_revert:
            if command.is_undo_rte:
                new_value = str(getattr(extracted_data, field_name.value) or '')
                new_formatted_value = new_value
            else:
                reverted_value = await self.field_repository.get_previous_value(conversation_id, field_name)

                if reverted_value:
                    new_value = str(reverted_value.FieldValue or '')
                    new_formatted_value = str(reverted_value.FormattedFieldValue or new_value)
                else:
                    # Fallback to aggregated data if no previous version exists
                    new_value = str(getattr(extracted_data, field_name.value) or '')
                    new_formatted_value = new_value

            await self.field_repository.create(
                conversation_id=conversation_id,
                field_name=field_name,
                field_value=new_value,
                formatted_field_value=new_formatted_value,
            )
            qual_fields[field_name.value] = TextField(context=new_value, snippet='')

        return MessageValidator(
            conversation_id=conversation_id,
            role=MessageRole.SYSTEM,
            type=MessageType.TEXT,
            content=engagement_templates.general.undo_success,
            system_reply_type=SystemReplyType.FIELD_SAVED,
            qual_fields=QualFields.model_validate(qual_fields),
            is_undoable=False,
        )

    async def _determine_chat_undo_fields(self, conversation_id: UUID) -> list[QualFieldName]:
        """
        Determine which fields to revert for a chat undo command.

        Implements intelligent batch undo logic by analyzing recent field changes.
        If a summary field was just changed, it's likely due to a source field update.
        In that case, this method identifies and includes the source field in the undo
        operation along with the dependent summary fields.

        Args:
            conversation_id: The conversation ID.

        Returns:
            A list of QualFieldName enums to be reverted.
        """
        recent_changes = await self.field_repository.get_recent_field_changes(conversation_id, limit=3)
        if not recent_changes:
            return []

        # The FieldName from the repository can be inferred as Column[str] by the linter.
        # We cast it to QualFieldName to ensure correct type checking.
        recent_fields = [QualFieldName(c.FieldName) for c in recent_changes]

        most_recent_field = recent_fields[0]
        dependent_fields = {QualFieldName.ENGAGEMENT_SUMMARY, QualFieldName.ONE_LINE_DESCRIPTION}

        # If the last change was to a dependent summary field, find the original source of the change.
        if most_recent_field in dependent_fields:
            source_field = next((field for field in recent_fields if field.is_regenerator), None)
            if source_field:
                # Batch undo: revert the source field and both dependent summary fields.
                fields_to_revert = {source_field, *dependent_fields}
                return list(fields_to_revert)

        # Default case: undo the most recent field. If it regenerates summaries, undo them too.
        fields_to_revert = {most_recent_field}
        if most_recent_field.is_regenerator:
            fields_to_revert.update(dependent_fields)

        return list(fields_to_revert)

    async def _regenerate_summaries_if_needed(self, command: Command, conversation_id: UUID) -> dict:
        """
        Regenerate summaries if one of the trigger fields was updated.
        """
        if not command.field_name.is_regenerator:
            return {}

        confirmed_data = await self.conversation_repository.get_confirmed_data(conversation_id)
        tense = confirmed_data.tense if confirmed_data and confirmed_data.tense else Tense.PRESENT

        context = await self._get_summaries_context(conversation_id, command)

        try:
            engagement_summary = await self._regenerate_field(context, QualFieldName.ENGAGEMENT_SUMMARY, tense=tense)
            one_line_summary = await self._regenerate_field(context, QualFieldName.ONE_LINE_DESCRIPTION, tense=tense)
        except Exception as e:
            logger.error(f'Failed to regenerate summaries: {e}')
            return {}

        try:
            await self.field_repository.create(
                conversation_id=conversation_id,
                field_name=QualFieldName.ENGAGEMENT_SUMMARY,
                field_value=engagement_summary,
                formatted_field_value=engagement_summary,
            )
            await self.field_repository.create(
                conversation_id=conversation_id,
                field_name=QualFieldName.ONE_LINE_DESCRIPTION,
                field_value=one_line_summary,
                formatted_field_value=one_line_summary,
            )
        except Exception as e:
            logger.error(f'Failed to save regenerated summaries: {e}')
            return {}

        return {
            QualFieldName.ENGAGEMENT_SUMMARY.value: TextField(context=engagement_summary, snippet=''),
            QualFieldName.ONE_LINE_DESCRIPTION.value: TextField(context=one_line_summary, snippet=''),
        }

    async def _get_summaries_context(self, conversation_id: UUID, command: Command) -> str:
        field_names = [
            QualFieldName.BUSINESS_ISSUES,
            QualFieldName.SCOPE_APPROACH,
            QualFieldName.VALUE_DELIVERED,
        ]
        field_values = {}
        for field_name in field_names:
            if field_name == command.field_name:
                field_values[field_name] = command.context
            else:
                latest_field = await self.field_repository.get_latest_value(conversation_id, field_name)
                field_values[field_name] = (
                    str(latest_field.FieldValue)
                    if latest_field is not None and latest_field.FieldValue is not None
                    else ''
                )

        summary_prompt = (
            f'Business Issues: {field_values[QualFieldName.BUSINESS_ISSUES]}\n\n'
            f'Scope and Approach: {field_values[QualFieldName.SCOPE_APPROACH]}\n\n'
            f'Value Delivered and Impact: {field_values[QualFieldName.VALUE_DELIVERED]}'
        )
        return summary_prompt

    async def _regenerate_field(self, context: str, field_name: QualFieldName, tense: str) -> str:
        regenerate_data = self._REGENERATE_MAP[field_name]

        system_prompt_obj: SummaryPrompts = regenerate_data['system_prompt']
        user_prompt_obj: SummaryPrompts = regenerate_data['user_prompt']

        system_prompt = system_prompt_obj.get_system_prompt(tense=tense)
        user_prompt = user_prompt_obj.get_user_prompt(text=context, tense=tense)
        max_tokens = regenerate_data['max_tokens']
        response_format = regenerate_data['response_format']

        messages: list[ChatCompletionMessageParam] = [
            {'role': 'system', 'content': system_prompt},
            {'role': 'user', 'content': user_prompt},
        ]
        model = settings.openai.model
        temperature = settings.openai.default_temperature

        try:
            response = await self.openai_repository.generate_chat_completion(
                model=model,
                messages=messages,
                temperature=temperature,
                max_tokens=max_tokens,
                response_format=response_format,
            )
        except Exception as e:
            logger.error(f'Failed to regenerate field {field_name}: {e}')
            raise

        if not response:
            logger.warning(f'Empty response received for field {field_name}')
            return ''

        return str(response)

    def _generate_data_to_analyze(
        self,
        full_text: str,
        snippet: str,
        result_length: int | None,
        content: str | None = None,
    ) -> dict:
        snippet_index = full_text.find(snippet)
        if snippet_index == -1:
            logger.warning('Snippet not found in full_text; returning original text unchanged.')
            result = {
                'full_text': full_text,
                'snippet': snippet,
                'text_before_snippet': '',
                'text_after_snippet': '',
                'result_length': result_length,
            }
            if content is not None:
                result['user_request'] = content
            return result
        text_before_snippet = full_text[:snippet_index]
        text_after_snippet = full_text[snippet_index + len(snippet) :]

        data_to_analyze = {
            'full_text': full_text,
            'snippet': snippet,
            'text_before_snippet': text_before_snippet,
            'text_after_snippet': text_after_snippet,
            'result_length': result_length,
        }
        if content is not None:
            data_to_analyze['user_request'] = content
        return data_to_analyze
