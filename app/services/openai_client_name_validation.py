import logging

from openai.types.chat import (
    ChatCompletionMessageParam,
    ChatCompletionSystemMessageParam,
    ChatCompletionUserMessageParam,
)
from pydantic import BaseModel, Field

from constants.prompt import prompt_templates
from repositories.openai import OpenAIRepository


__all__ = ['OpenAIClientNameValidationService']

logger = logging.getLogger(__name__)


class IsClientNameResponse(BaseModel):
    is_client_name: bool = Field(
        ...,
        description='Whether the input is a client name.',
    )


class OpenAIClientNameValidationService:
    def __init__(self, openai_repository: OpenAIRepository):
        self.openai_repository = openai_repository
        self.system_prompt = prompt_templates.client_name_validation.SYSTEM
        self.user_prompt = prompt_templates.client_name_validation.USER

    async def validate(self, text: str) -> bool:
        """
        Validate if the given text is a plausible client name.

        Args:
            text: The text to validate.

        Returns:
            bool: True if the text is a client name, False otherwise.
        """
        try:
            messages: list[ChatCompletionMessageParam] = [
                ChatCompletionSystemMessageParam(role='system', content=self.system_prompt),
                ChatCompletionUserMessageParam(role='user', content=self.user_prompt.format(text=text)),
            ]
            response = await self.openai_repository.generate_chat_completion(
                messages=messages,
                temperature=0.0,
                response_format=IsClientNameResponse,
            )
            if isinstance(response, IsClientNameResponse):
                return response.is_client_name
            return False
        except Exception:
            logger.exception('Error validating client name')
            return False
