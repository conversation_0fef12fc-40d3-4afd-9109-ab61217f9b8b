import logging
from typing import Any

from repositories.user_info import UserInfoRepository


__all__ = ['UserInfoService']

logger = logging.getLogger(__name__)


class UserInfoService:
    def __init__(self, user_info_repository: UserInfoRepository):
        self.user_info_repository = user_info_repository

    async def get(self, deloitte_id: str, token: str) -> dict[str, dict[str, Any]]:
        """
        Get user information by Deloitte ID.

        Args:
            deloitte_id: The Deloitte identifier for the user.
            token: Authentication token for the request.

        Returns:
            dict[str, dict[str, Any]]: User info

        Raises:
            Exception: If an error occurs while getting user info.
        """
        try:
            logger.info('Getting user info')
            return await self.user_info_repository.get(deloitte_id, token)
        except Exception as e:
            logger.error('Error getting user info for deloitte_id %s: %s', deloitte_id, str(e), exc_info=True)
            raise
