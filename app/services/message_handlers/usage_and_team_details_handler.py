import logging
from uuid import UUI<PERSON>

from fastapi import UploadFile

from constants.engagement import engagement_templates
from constants.message import MessageRole, MessageType, PageType, SystemReplyType
from repositories.conversation_message import ConversationMessageRepository
from schemas import CombinedMessageSerializer, Command, Option
from schemas.conversation_message.message import MessageValidator, SystemMessageSerializer, UserMessageSerializer
from services.message_handlers.base import MessageHandler


__all__ = ['UsageAndTeamDetailsMessageHandler']

logger = logging.getLogger(__name__)


class UsageAndTeamDetailsMessageHandler(MessageHandler):
    def __init__(self, conversation_message_repository: ConversationMessageRepository):
        self.conversation_message_repository = conversation_message_repository

    async def handle(
        self,
        conversation_id: UUID,
        content: str,
        selected_option: Option | None,
        command: Command | None,  # argument is not used for purpose to keep the same signature as other handlers
        files: list[UploadFile] | None,
        token: str,
    ) -> CombinedMessageSerializer:
        page_type = PageType.USAGE_AND_TEAM_DETAILS
        user_message_to_persist = MessageValidator(
            conversation_id=conversation_id,
            role=MessageRole.USER,
            type=MessageType.TEXT,
            content=content,
            selected_option=selected_option,
            files=files,
            system_reply_type=None,
            page_type=page_type,
        )
        user_message = await self.conversation_message_repository.create(user_message_to_persist)

        system_message_to_persist = MessageValidator(
            conversation_id=conversation_id,
            role=MessageRole.SYSTEM,
            type=MessageType.TEXT,
            content=engagement_templates.general.sorry_cant_help_reply,
            system_reply_type=SystemReplyType.ENGAGEMENT_DETAILS_SORRY_CANT_HELP,
            is_error=False,
            page_type=page_type,
        )
        system_message = await self.conversation_message_repository.create(system_message_to_persist)
        return CombinedMessageSerializer(
            user=UserMessageSerializer.model_validate(user_message),
            system=SystemMessageSerializer.model_validate(system_message),
        )
