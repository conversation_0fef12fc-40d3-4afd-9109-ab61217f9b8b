import json
import logging
from typing import Any

from cachetools import T<PERSON><PERSON><PERSON>

from repositories import BlobStorageRepository, EngagementLocationRepository


__all__ = ['EngagementLocationDataService']


logger = logging.getLogger(__name__)


class EngagementLocationDataService:
    """Service for engagement locations operations."""

    _cache: TTLCache = TTLCache(maxsize=1, ttl=60 * 60 * 1)  # Cache for 1 hour
    _CACHE_KEY = 'engagement_locations'

    def __init__(
        self, engagement_location_repository: EngagementLocationRepository, blob_repository: BlobStorageRepository
    ):
        self.engagement_location_repository = engagement_location_repository
        self.blob_repository = blob_repository

    def prepare_cached_result(self, data: list[dict]) -> dict[str, Any]:
        result = {location['name']: location['id'] for location in data if {'id', 'name'}.issubset(location)}
        return result

    async def list(self, token: str) -> dict[str, Any]:
        """
        List all engagement locations.

        Returns:
            List of engagement locations.

        Raises:
            Exception: If an error occurs while listing engagement locations.
        """
        try:
            logger.debug('Getting cached engagement locations')
            engagement_locations = self._cache.get(self._CACHE_KEY)
            if engagement_locations is not None:
                logger.debug('Returning engagement locations from cache')
                return engagement_locations

            logger.info('Listing engagement locations')
            engagement_locations = await self.engagement_location_repository.list(token)
            cached_result = self.prepare_cached_result(engagement_locations)
            self._cache[self._CACHE_KEY] = cached_result

            await self.blob_repository.upload(
                'engagement-locations.json', json.dumps(cached_result).encode('utf-8'), 'application/json'
            )

            return self._cache[self._CACHE_KEY]
        except Exception as e:
            logger.error('Error listing engagement locations: %s', e)
            raise
