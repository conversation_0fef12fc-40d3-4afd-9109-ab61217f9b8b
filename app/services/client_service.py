import json
import logging
from typing import Any

from cachetools import TTLCache

from repositories import BlobStorageRepository, ServiceRepository


__all__ = ['ClientServiceDataService']


logger = logging.getLogger(__name__)


class ClientServiceDataService:
    """Service for client services operations."""

    _cache: TTLCache = TTLCache(maxsize=1, ttl=60 * 60 * 1)  # Cache for 1 hour
    _CACHE_KEY = 'client_services'

    def __init__(self, service_repository: ServiceRepository, blob_repository: BlobStorageRepository):
        self.service_repository = service_repository
        self.blob_repository = blob_repository

    def prepare_cached_result(self, data: list[dict]) -> dict[str, Any]:
        def process_node(node: dict, node_result: dict | None = None) -> dict[str, Any]:
            if node_result is None:
                node_result = {}

            if node.get('nodes') == [] and 'name' in node and 'fullPath' in node and node['fullPath'] is not None:
                node_result[node['name']] = {
                    'aliases': node.get('aliases'),
                    'fullPath': node['fullPath'],
                    'locationId': node.get('locationId'),
                    'externalId': node.get('externalId'),
                    'globalId': node.get('globalId'),
                    'externalGlobalId': node.get('externalGlobalId'),
                    'parents': node.get('parents'),
                    'riskAliases': node.get('riskAliases', []),
                    'id': node.get('id'),
                }

            for child in node.get('nodes', {}):
                process_node(child, node_result)

            return node_result

        result = {k: v for row in data for k, v in process_node(row).items()}
        return result

    async def list(self, token: str) -> dict[str, Any]:
        """
        List all client services.

        Returns:
            List of client services.

        Raises:
            Exception: If an error occurs while listing client services.
        """
        try:
            logger.debug('Getting cached client services')
            client_services = self._cache.get(self._CACHE_KEY)
            if client_services is not None:
                logger.debug('Returning client services from cache')
                return client_services

            logger.info('Listing client services')
            client_services = await self.service_repository.list(token)
            cached_result = self.prepare_cached_result(client_services)
            self._cache[self._CACHE_KEY] = cached_result

            await self.blob_repository.upload(
                'client-services.json', json.dumps(cached_result).encode('utf-8'), 'application/json'
            )

            return self._cache[self._CACHE_KEY]
        except Exception as e:
            logger.error('Error listing client services: %s', e)
            raise
