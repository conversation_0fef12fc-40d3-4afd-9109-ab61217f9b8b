from dataclasses import dataclass
import json
import logging
from typing import Any, Type, TypeVar, cast

from openai import BadRequestError
from openai.types.chat import (
    ChatCompletionMessageParam,
    ChatCompletionSystemMessageParam,
    ChatCompletionUserMessageParam,
)

from config import settings
from constants.engagement import EngagementMessageIntention
from constants.message import ConversationMessageIntention
from core.schemas import CustomModel
from exceptions import AIBadRequestError
from repositories import OpenAIRepository
from schemas.conversation_message import ConversationMessageIntentClassifierServiceResponse
from schemas.engagement_message import EngagementMessageIntentClassifierServiceResponse


__all__ = ['IntentClassifierService']


logger = logging.getLogger(__name__)

IntentEnumT = TypeVar('IntentEnumT', ConversationMessageIntention, EngagementMessageIntention)
ResponseT = TypeVar(
    'ResponseT', ConversationMessageIntentClassifierServiceResponse, EngagementMessageIntentClassifierServiceResponse
)


class IntentInfo(CustomModel):
    """Information about an intent."""

    name: str
    description: str
    user_message_examples: list[str]


@dataclass(frozen=True)
class IntentClassifierService:
    """Classifies user messages into intents."""

    openai_service: OpenAIRepository
    system_prompt_template: str
    user_prompt_template: str
    intentions: dict[str, Any]
    temperature: float = settings.openai.default_temperature
    intent_object_structure: str | None = None

    def _get_user_prompt(
        self,
        *,
        user_message: str,
    ) -> str:
        return self.user_prompt_template.format(
            user_message=user_message,
        )

    def _get_system_prompt(self, intention_enum: Type[IntentEnumT]) -> str:
        format_kwargs = {}
        if '{intent_list}' in self.system_prompt_template:
            intent_list = [intent['intentionName'] for intent in self.intentions['Intentions']]
            format_kwargs['intent_list'] = json.dumps(intent_list)

        if '{intents_descriptions}' in self.system_prompt_template:
            format_kwargs['intents_descriptions'] = self._get_formatted_intents_info(intention_enum)

        if '{intent_object_structure}' in self.system_prompt_template:
            if self.intent_object_structure is None:
                raise ValueError('intent_object_structure must be provided for this prompt template')
            format_kwargs['intent_object_structure'] = self.intent_object_structure

        return self.system_prompt_template.format(**format_kwargs)

    def _get_formatted_intents_info(self, intention_enum: Type[IntentEnumT]) -> str:
        """
        Build a prompt string containing descriptions of all intents in this enum.

        Returns:
        A json-string with all intent descriptions.
        """

        intents_info = self.get_intents_info(intention_enum)
        serializable_intents_info = {intent.value: info.model_dump() for intent, info in intents_info.items()}
        return json.dumps(serializable_intents_info, indent=2)

    def get_intents_info(self, intention_enum: Type[IntentEnumT]) -> dict[IntentEnumT, IntentInfo]:
        intentions = self.intentions['Intentions']
        info = {}
        for intention in intentions:
            name = intention['intentionName']
            try:
                info[intention_enum(name)] = IntentInfo(
                    name=name,
                    description=intention['description'],
                    user_message_examples=intention['userMessageExamples'],
                )
            except ValueError:
                logger.exception(
                    'Couldnt parse intention name %s from file to %s',
                    name,
                    intention_enum.__name__,
                )
                raise
        return info

    @staticmethod
    def _create_system_message(message: str) -> ChatCompletionSystemMessageParam:
        """Create a system message for the chat completion."""
        return {'role': 'system', 'content': message}

    @staticmethod
    def _create_user_message(message: str) -> ChatCompletionUserMessageParam:
        """Create a user message for the chat completion."""
        return {'role': 'user', 'content': message}

    async def classify_intent(
        self,
        *,
        user_message: str,
        response_cls: Type[ResponseT],
        intention_enum: Type[IntentEnumT],
    ) -> ResponseT:
        """
        Classify the user's message intent using an LLM.

        Args:
            user_message: The user's message content.

        Returns:
            The classified intent.
        """

        # Prepare the messages
        system_message = self._create_system_message(self._get_system_prompt(intention_enum))
        user_message_param = self._create_user_message(
            self._get_user_prompt(
                user_message=user_message,
            )
        )

        # Call the OpenAI API
        try:
            response = await self.openai_service.generate_chat_completion(
                messages=[
                    cast(ChatCompletionMessageParam, system_message),
                    cast(ChatCompletionMessageParam, user_message_param),
                ],
                temperature=self.temperature,
                response_format=response_cls,
            )
        except BadRequestError:
            raise AIBadRequestError()

        if not isinstance(response, response_cls):
            raise RuntimeError(f'Invalid response from model, expected {response_cls}, got {type(response)}')

        return response
