import logging
from typing import Dict
from uuid import UUID

from openai.types.chat import Chat<PERSON><PERSON>pletionMessageParam

from constants.engagement import EngagementMessageIntention
from constants.extracted_data import DataSourceType
from constants.message import QualFieldName
from constants.prompt import prompt_templates
from repositories import ExtractedDataRepository, FieldRepository, OpenAIRepository
from schemas import EngagementFieldModificationResponse, ExtractedData

from .extracted_data import ExtractedDataService


__all__ = ['EngagementFieldModificationService']


logger = logging.getLogger(__name__)


class EngagementFieldModificationService:
    """
    Service for modifying specific engagement fields through conversational messages.

    This service handles the complete flow of:
    1. Retrieving current aggregated data
    2. Processing user input through OpenAI API to generate updated field content
    3. Persisting modified data back to storage
    4. Returning structured response for system message generation
    """

    # Intent to field mapping for engagement modifications
    INTENT_TO_FIELD_MAP: Dict[EngagementMessageIntention, QualFieldName] = {
        EngagementMessageIntention.BUSINESS_ISSUES: QualFieldName.BUSINESS_ISSUES,
        EngagementMessageIntention.SCOPE_APPROACH: QualFieldName.SCOPE_APPROACH,
        EngagementMessageIntention.VALUE_DELIVERED_IMPACT: QualFieldName.VALUE_DELIVERED,
        EngagementMessageIntention.ENGAGEMENT_SUMMARY: QualFieldName.ENGAGEMENT_SUMMARY,
        EngagementMessageIntention.ONE_LINE_DESCRIPTION: QualFieldName.ONE_LINE_DESCRIPTION,
    }

    def __init__(
        self,
        extracted_data_service: ExtractedDataService,
        extracted_data_repository: ExtractedDataRepository,
        field_repository: FieldRepository,
        openai_repository: OpenAIRepository,
    ):
        """
        Initialize the engagement field modification service.

        Args:
            extracted_data_service: Service for aggregating data from multiple sources
            extracted_data_repository: Repository for persisting extracted data
            field_repository: Repository for persisting field data
            openai_repository: Repository for OpenAI API interactions
        """
        self.extracted_data_service = extracted_data_service
        self.extracted_data_repository = extracted_data_repository
        self.field_repository = field_repository
        self.openai_repository = openai_repository

    async def modify_field(
        self,
        conversation_id: UUID,
        intent: EngagementMessageIntention,
        user_request: str,
    ) -> EngagementFieldModificationResponse:
        """
        Modify a specific engagement field based on user intent and request.

        Args:
            conversation_id: The conversation ID
            intent: The engagement field modification intent
            user_request: The user's modification request
            token: User token for external API calls

        Returns:
            Dict containing the modification result with field name and updated content

        Raises:
            ValueError: If the intent is not supported for field modification
            Exception: If there's an error during field modification
        """
        logger.debug(
            'Starting field modification for conversation %s, intent %s, request: %s',
            conversation_id,
            intent,
            user_request,
        )

        # Validate intent is supported
        if intent not in self.INTENT_TO_FIELD_MAP:
            raise ValueError(f'Intent {intent} is not supported for field modification')

        field_name = self.INTENT_TO_FIELD_MAP[intent]

        try:
            # Step 1: Retrieve current aggregated data
            logger.debug('Retrieving aggregated data for conversation %s', conversation_id)
            aggregated_data = await self.extracted_data_service.aggregate_data(conversation_id)

            # Handle field value - some fields are lists, others are strings
            raw_field_value = getattr(aggregated_data, field_name.value, None)
            if isinstance(raw_field_value, list):
                current_field_value = ' | '.join(raw_field_value) if raw_field_value else ''
            else:
                current_field_value = raw_field_value or ''
            logger.debug('Current field value for %s: %s', field_name, current_field_value)

            # Step 2: Generate updated field content using OpenAI
            logger.debug('Generating updated content for field %s', field_name)
            updated_content = await self._generate_updated_field_content(
                current_text=current_field_value,
                user_request=user_request,
            )
            logger.debug('Generated content: %s', updated_content)

            if updated_content is None:
                logger.warning('Failed to generate updated content for field %s', field_name)
                return EngagementFieldModificationResponse(
                    success=False,
                    field_name=field_name.value,
                    updated_content=None,
                    original_content=None,
                    error='Failed to generate updated content',
                )

            # Step 3: Save modified data to storage
            await self._save_modified_field_data(
                conversation_id=conversation_id,
                field_name=field_name,
                updated_content=updated_content,
            )

            logger.info('Successfully modified field %s for conversation %s', field_name, conversation_id)

            return EngagementFieldModificationResponse(
                success=True,
                field_name=field_name.value,
                updated_content=updated_content,
                original_content=current_field_value,
                error=None,
            )

        except Exception as e:
            logger.error('Error modifying field %s for conversation %s: %s', field_name, conversation_id, e)
            raise

    async def _generate_updated_field_content(
        self,
        current_text: str,
        user_request: str,
    ) -> str | None:
        """
        Generate updated field content using OpenAI API.

        Args:
            current_text: The current field content
            user_request: The user's modification request

        Returns:
            Updated field content or None if generation failed
        """
        try:
            # Temporary hardcoded prompts for testing
            system_prompt = prompt_templates.engagement_field_changer.SYSTEM
            user_prompt = prompt_templates.engagement_field_changer.USER.format(
                text=current_text,
                user_request=user_request,
            )

            messages: list[ChatCompletionMessageParam] = [
                {'role': 'system', 'content': system_prompt},
                {'role': 'user', 'content': user_prompt},
            ]

            updated_content = await self.openai_repository.generate_text_completion(
                messages=messages,
                temperature=0.0,
                max_completion_tokens=1000,
            )

            return updated_content

        except Exception:
            logger.exception('Error generating updated field content')
            return None

    async def _save_modified_field_data(
        self,
        conversation_id: UUID,
        field_name: QualFieldName,
        updated_content: str,
    ) -> None:
        """
        Save modified field data to extracted data storage and field history.

        Args:
            conversation_id: The conversation ID
            field_name: The name of the modified field
            updated_content: The updated field content
        """
        try:
            # Get or create extracted data for prompt source type
            extracted_data = await self.extracted_data_repository.get(
                conversation_id=conversation_id,
                data_source_type=DataSourceType.PROMPT,
            )

            if extracted_data is None:
                extracted_data = ExtractedData.create(
                    conversation_id=conversation_id,
                    data_source_type=DataSourceType.PROMPT,
                )

            # Update the specific field
            setattr(extracted_data, field_name.value, updated_content)

            # Save to repository
            await self.extracted_data_repository.update(extracted_data)

            await self.field_repository.create(
                conversation_id=conversation_id,
                field_name=field_name,
                field_value=updated_content,
                formatted_field_value=updated_content,
            )

            logger.debug(
                'Saved modified field %s for conversation %s with content length %d',
                field_name,
                conversation_id,
                len(updated_content),
            )

        except Exception as e:
            logger.error('Error saving modified field %s for conversation %s: %s', field_name, conversation_id, e)
            raise
