from datetime import date
import logging
from typing import Any

from schemas import ExtractedData


__all__ = ['PromptAndDocumentDataParser']


logger = logging.getLogger(__name__)


class PromptAndDocumentDataParser:
    @classmethod
    def __call__(cls, extracted_data: ExtractedData, document_data: dict[str, Any]) -> ExtractedData:
        """
        Parse document or prompt extracted data and update the ExtractedData object.

        Args:
            extracted_data: The existing ExtractedData object to update
            document_data: Raw data extracted from documents or prompts

        Returns:
            Updated ExtractedData object
        """
        result = extracted_data.model_copy()

        if client_names := document_data.get('client_name'):
            result.client_name = client_names

        if ldmf_countries := document_data.get('ldmf_country'):
            result.ldmf_country = ldmf_countries

        if title := document_data.get('title'):
            result.title = title

        if start_date := document_data.get('start_date'):
            result.start_date = cls._get_date_for_extracted_data(start_date)

        if end_date := document_data.get('end_date'):
            result.end_date = cls._get_date_for_extracted_data(end_date)

        if start_date_original := document_data.get('start_date_original'):
            result.start_date_original = start_date_original

        if end_date_original := document_data.get('end_date_original'):
            result.end_date_original = end_date_original

        if objective_and_scope := document_data.get('objective_and_scope'):
            result.objective_and_scope = objective_and_scope

        if outcomes := document_data.get('outcomes'):
            result.outcomes = outcomes

        return result

    @staticmethod
    def _get_date_for_extracted_data(val: Any) -> date | None:
        if val is None or isinstance(val, date):
            return val
        if isinstance(val, str):
            try:
                return date.fromisoformat(val)
            except ValueError:
                logger.warning('A document date of an unexpected format was detected: %s', val)
                return None
        else:
            logger.warning('A document date of an unexpected type was detected: %s', type(val))
            return None
