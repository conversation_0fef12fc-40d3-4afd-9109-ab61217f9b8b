"""Enhanced merge unique strategy for additional fields."""

from schemas import ExtractedData
from schemas.extracted_data import IndustryData, ServiceData

from .base import BaseAggregationStrategy


class EnhancedMergeUniqueValuesStrategy(BaseAggregationStrategy):
    """Strategy for merging unique values from all sources for additional fields."""

    def __init__(self):
        # Existing fields
        self._client_names: set[str] = set()
        self._ldmf_countries: set[str] = set()

        # New fields that need unique value merging
        self._client_industries: set[IndustryData] = set()
        self._engagement_locations: set[str] = set()
        self._client_services: set[ServiceData] = set()

    def process(self, extracted_data: ExtractedData) -> None:
        """Merge unique values for specified fields."""
        # Existing fields
        if extracted_data.client_name:
            self._client_names.update(extracted_data.client_name)
        if extracted_data.ldmf_country:
            self._ldmf_countries.update(extracted_data.ldmf_country)

        # New fields
        if extracted_data.client_industries:
            self._client_industries.update(extracted_data.client_industries)
        if extracted_data.engagement_locations:
            self._add_unique_values(self._engagement_locations, extracted_data.engagement_locations)
        if extracted_data.client_services:
            self._client_services.update(extracted_data.client_services)

    def _add_unique_values(self, target_set: set[str], value: str | list[str]) -> None:
        """Add unique values to target set, handling both string and list inputs."""
        if isinstance(value, str):
            # Split by common separators and clean up
            items = [item.strip() for item in value.replace(',', '|').replace(';', '|').split('|')]
            target_set.update(item for item in items if item)
        elif isinstance(value, list):
            target_set.update(item.strip() for item in value if item and item.strip())

    def get_client_names(self) -> list[str]:
        """Get sorted list of unique client names."""
        return sorted(list(self._client_names))

    def get_ldmf_countries(self) -> list[str]:
        """Get sorted list of unique LDMF countries."""
        return sorted(list(self._ldmf_countries))

    def get_client_industries(self) -> list[IndustryData]:
        """Get sorted list of unique client industries."""
        return sorted(list(self._client_industries), key=lambda x: x.name)

    def get_engagement_locations(self) -> list[str]:
        """Get sorted list of unique engagement locations."""
        return sorted(list(self._engagement_locations))

    def get_client_services(self) -> list[ServiceData]:
        """Get sorted list of unique client services."""
        return sorted(list(self._client_services), key=lambda x: x.name)
