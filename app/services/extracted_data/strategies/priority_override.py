from schemas import ExtractedData

from .base import BaseAggregationStrategy


__all__ = ['PriorityOverrideStrategy']


class PriorityOverrideStrategy(BaseAggregationStrategy):
    """Strategy for priority-based override (highest priority source wins)."""

    def __init__(self):
        self._objective_and_scope: str | None = None
        self._outcomes: str | None = None
        self._source_of_work: str | None = None

    def process(self, extracted_data: ExtractedData) -> None:
        """Apply priority-based override for objective_and_scope and outcomes."""
        # Priority-based override: later sources (higher priority) override earlier ones
        self._objective_and_scope = extracted_data.objective_and_scope or self._objective_and_scope
        self._outcomes = extracted_data.outcomes or self._outcomes
        self._source_of_work = extracted_data.source_of_work or self._source_of_work

    def get_objective_and_scope(self) -> str | None:
        """Get the objective and scope with highest priority."""
        return self._objective_and_scope

    def get_outcomes(self) -> str | None:
        """Get the outcomes with highest priority."""
        return self._outcomes

    def get_source_of_work(self) -> str | None:
        """Get the source of work with highest priority."""
        return self._source_of_work
