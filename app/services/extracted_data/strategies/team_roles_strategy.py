"""Team roles aggregation strategy for properly merging team members from different sources."""

import json
import logging
from typing import Any, Dict

from schemas import ExtractedData

from .base import BaseAggregationStrategy


logger = logging.getLogger(__name__)


class TeamRolesAggregationStrategy(BaseAggregationStrategy):
    """Strategy for merging team roles from different sources with proper deduplication by email."""

    def __init__(self):
        self._team_roles_values: Dict[str, Dict[str, Any]] = {}  # email -> team member data

    def process(self, extracted_data: ExtractedData) -> None:
        """Process team_roles from extracted data, deduplicating by email."""
        if not extracted_data.team_roles:
            return

        try:
            # Parse team_roles JSON string to get structured data
            team_roles_data = json.loads(extracted_data.team_roles)

            # Merge team members, using email as unique key (same logic as ExtractedDataMerger)
            for user in team_roles_data:
                if email := user.get('email'):
                    if email not in self._team_roles_values:
                        self._team_roles_values[email] = user
                    else:
                        existing_roles = self._team_roles_values[email]['roles']
                        self._team_roles_values[email]['roles'] = existing_roles + user.get('roles', [])

                        is_contact = self._team_roles_values[email].get('is_contact', False)
                        self._team_roles_values[email]['is_contact'] = is_contact or user.get('is_contact', False)

                        is_approver = self._team_roles_values[email].get('is_approver', False)
                        self._team_roles_values[email]['is_approver'] = is_approver or user.get('is_approver', False)

        except (json.JSONDecodeError, TypeError) as e:
            logger.warning('Failed to parse team_roles JSON: %s', e)

    def get_team_roles_json(self) -> list[dict[str, Any]] | None:
        """Get merged team roles as JSON string."""
        if not self._team_roles_values:
            return None

        return list(self._team_roles_values.values())
