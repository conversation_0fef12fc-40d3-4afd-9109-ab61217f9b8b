import logging
from typing import Any
from uuid import UUID

from constants.extracted_data import FieldStatus
from constants.message import SystemReplyType
from schemas import AggregatedData, FieldHandlerResponse
from schemas.confirmed_data import ConfirmedData

from .base import BaseFieldHandler


__all__ = ['OutcomesHandler']
logger = logging.getLogger(__name__)


class OutcomesHandler(BaseFieldHandler):
    """Handler for RequiredField.OUTCOMES."""

    async def check_and_get_response(
        self,
        aggregated_data: AggregatedData,
        confirmed_data: ConfirmedData,
        called_from: str = '',
        conversation_id: UUID | None = None,
    ) -> FieldHandlerResponse:
        logger.info('Called OutcomesHandler.check_and_get_response from %s', called_from)

        # Check if outcomes are confirmed
        if confirmed_data.outcomes is not None:
            return FieldHandlerResponse(
                needs_confirmation=False,
                system_message=None,
                system_reply_type=None,
                next_expected_field=None,
                field_status=FieldStatus.CONFIRMED,
            )
        # Check if outcomes are in aggregated data
        elif aggregated_data.outcomes is not None:
            reply_type = SystemReplyType.OUTCOMES_AGGREGATED_QUESTION
            system_message = reply_type.message_text.format(outcomes=aggregated_data.outcomes)
            return FieldHandlerResponse(
                needs_confirmation=True,
                field_status=FieldStatus.SINGLE,
                system_message=system_message,
                system_reply_type=reply_type,
                next_expected_field=None,
            )
        # Neither confirmed nor aggregated
        else:
            reply_type = SystemReplyType.NEED_INFO_OUTCOMES
            system_message = reply_type.message_text
            return FieldHandlerResponse(
                needs_confirmation=True,
                field_status=FieldStatus.MISSING,
                system_message=system_message,
                system_reply_type=reply_type,
                next_expected_field=None,
            )

    async def validate_value(
        self, field_value: Any, called_from: str = '', conversation_id: UUID | None = None
    ) -> FieldHandlerResponse:
        return await self.check_and_get_response(field_value, ConfirmedData(), called_from, conversation_id)
