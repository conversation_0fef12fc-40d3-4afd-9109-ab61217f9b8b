import logging
from typing import Any
from uuid import UUID

from constants.extracted_data import FieldStatus, RequiredField
from constants.message import SystemReplyType
from schemas import AggregatedData, FieldHandlerResponse
from schemas.confirmed_data import ConfirmedData

from .base import BaseFieldHandler


__all__ = ['DateIntervalsHandler']
logger = logging.getLogger(__name__)


class DateIntervalsHandler(BaseFieldHandler):
    """Handler for RequiredField.ENGAGEMENT_DATES."""

    async def check_and_get_response(
        self,
        aggregated_data: AggregatedData,
        confirmed_data: ConfirmedData,
        called_from: str = '',
        conversation_id: UUID | None = None,
    ) -> FieldHandlerResponse:
        # Check if dates are already confirmed (BOTH start and end dates required)
        if confirmed_data.date_intervals:
            return FieldHandlerResponse(
                needs_confirmation=False,
                system_message=None,
                system_reply_type=None,
                next_expected_field=None,
                field_status=FieldStatus.CONFIRMED,
            )

        # Check if dates exist in aggregated data
        if aggregated_data.date_intervals:
            start_date, end_date = aggregated_data.date_intervals[0]
            if start_date or end_date:
                reply_type = (
                    SystemReplyType.DATES_UNAMBIGUOUS
                    if aggregated_data.is_date_unambiguous_and_complete
                    else SystemReplyType.DATES_AMBIGUOUS
                )
                system_message = reply_type.message_text
                return FieldHandlerResponse(
                    needs_confirmation=True,
                    field_status=FieldStatus.PENDING_CONFIRMATION,
                    system_message=system_message,
                    system_reply_type=reply_type,
                    next_expected_field=None,
                    options=[aggregated_data.date_intervals[0]],
                )

        # No dates found - ask user to provide them
        reply_type = SystemReplyType.EXTRACTED_DATA_DATE_INTERVAL_MISSING
        system_message = reply_type.message_text
        return FieldHandlerResponse(
            needs_confirmation=True,
            field_status=FieldStatus.MISSING,
            system_message=system_message,
            system_reply_type=reply_type,
            next_expected_field=RequiredField.ENGAGEMENT_DATES,
        )

    async def validate_value(
        self, field_value: Any, called_from: str = '', conversation_id: UUID | None = None
    ) -> FieldHandlerResponse:
        return await self.check_and_get_response(field_value, ConfirmedData(), called_from, conversation_id)
