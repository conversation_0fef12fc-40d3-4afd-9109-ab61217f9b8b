from abc import ABC, abstractmethod
from typing import Any
from uuid import UUID

from schemas import AggregatedData, FieldHandlerResponse
from schemas.confirmed_data import ConfirmedData


__all__ = ['BaseFieldHandler', 'TokenRequiredFieldHandler']


class BaseFieldHandler(ABC):
    """
    Abstract base class for handling missing required fields and generating prompts.
    """

    @abstractmethod
    async def check_and_get_response(
        self,
        aggregated_data: AggregatedData,
        confirmed_data: ConfirmedData,
        called_from: str = '',
        conversation_id: UUID | None = None,
    ) -> FieldHandlerResponse:
        """
        Checks if the specific field needs confirmation and returns structured response.

        Args:
            aggregated_data: The AggregatedData object to check.
            confirmed_data: The ConfirmedData object with user confirmations.
            called_from: String indicating where the method was called from.
            conversation_id: The conversation ID (optional).
        Returns:
            FieldHandlerResponse: Structured response indicating next steps.
        """
        ...

    @abstractmethod
    async def validate_value(
        self, field_value: Any, called_from: str = '', conversation_id: UUID | None = None
    ) -> FieldHandlerResponse:
        """
        Validates a single value for the field.

        Args:
            field_value: The value to validate
            called_from: String indicating where the method was called from.
            conversation_id: The conversation ID (optional).

        Returns:
            FieldHandlerResponse: Structured response indicating next steps.
        """
        ...


class TokenRequiredFieldHandler(ABC):
    """
    Abstract base class for handlers that require external API authentication.
    This interface is for handlers that need a token for external API calls.
    """

    @abstractmethod
    async def check_and_get_response(
        self,
        aggregated_data: AggregatedData,
        confirmed_data: ConfirmedData,
        token: str,
        called_from: str = '',
        conversation_id: UUID | None = None,
    ) -> FieldHandlerResponse:
        """
        Checks if the specific field needs confirmation and returns structured response.

        Args:
            aggregated_data: The AggregatedData object to check.
            confirmed_data: The ConfirmedData object with user confirmations.
            token: MSAL token from the user for API authentication
            called_from: String indicating where the method was called from.
            conversation_id: The conversation ID (optional).

        Returns:
            FieldHandlerResponse: Structured response indicating next steps.
        """
        ...

    @abstractmethod
    async def validate_value(
        self, token: str, field_value: Any, called_from: str = '', conversation_id: UUID | None = None
    ) -> FieldHandlerResponse:
        """
        Validates a single value for the field.

        Args:
            token: MSAL token from the user for API authentication
            field_value: The value to validate
            called_from: String indicating where the method was called from.
            conversation_id: The conversation ID (optional).

        Returns:
            FieldHandlerResponse: Structured response indicating next steps.
        """
        ...
