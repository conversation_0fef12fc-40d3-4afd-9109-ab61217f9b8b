import json
import logging
from typing import Any

from cachetools import TTLCache

from repositories import BlobStorageRepository, IndustryRepository


__all__ = ['ClientIndustryDataService']


logger = logging.getLogger(__name__)


class ClientIndustryDataService:
    """Service for project roles operations."""

    _cache: TTLCache = TTLCache(maxsize=1, ttl=60 * 60 * 1)  # Cache for 1 hour
    _CACHE_KEY = 'client_industries'

    def __init__(self, industry_repository: IndustryRepository, blob_repository: BlobStorageRepository):
        self.industry_repository = industry_repository
        self.blob_repository = blob_repository

    def prepare_cached_result(self, data: list[dict]) -> dict[str, dict[str, str]]:
        def process_node(node: dict, node_result: dict | None = None) -> dict[str, str]:
            if node_result is None:
                node_result = {}

            if 'name' in node and 'fullPath' in node and 'Not Applicable' not in node['name']:
                node_result[node['name']] = {
                    'aliases': node.get('aliases'),
                    'fullPath': node['fullPath'],
                    'locationId': node.get('locationId'),
                    'externalId': node.get('externalId'),
                    'globalId': node.get('globalId'),
                    'externalGlobalId': node.get('externalGlobalId'),
                    'parents': node.get('parents'),
                    'riskAliases': node.get('riskAliases', []),
                    'id': node.get('id'),
                }

            for child in node.get('nodes', {}):
                process_node(child, node_result)

            return node_result

        result = {}

        for row in data:
            ldmf_country = row.get('name')
            if not ldmf_country:
                continue

            if ldmf_country not in result:
                result[ldmf_country] = {}

            result[ldmf_country] = process_node(row)

        return result

    async def list(self, token: str) -> dict[str, Any]:
        """
        List all industries.

        Returns:
            List of industries.

        Raises:
            Exception: If an error occurs while listing industries.
        """
        try:
            logger.debug('Getting cached industries')
            industries = self._cache.get(self._CACHE_KEY)
            if industries is not None:
                logger.debug('Returning industries from cache')
                return industries

            logger.info('Listing industries')
            client_industries = await self.industry_repository.list(token)
            cached_result = self.prepare_cached_result(client_industries)
            self._cache[self._CACHE_KEY] = cached_result

            await self.blob_repository.upload(
                'client-industries.json', json.dumps(cached_result).encode('utf-8'), 'application/json'
            )

            return self._cache[self._CACHE_KEY]
        except Exception as e:
            logger.error('Error listing industries: %s', e)
            raise
