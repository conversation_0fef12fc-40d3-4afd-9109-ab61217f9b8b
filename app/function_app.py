import logging

import azure.durable_functions as df
import azure.functions as func

from durable_functions.activities import document_processing_acttivity_bp, enchanced_extraction_activity_bp
from durable_functions.entities import progress_tracker_bp
from durable_functions.orchestrators import document_processing_bp, enhanced_processing_bp, unified_processing_bp
from durable_functions.triggers import http_triggers_bp, queue_triggers_bp


logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

app = df.DFApp(http_auth_level=func.AuthLevel.ANONYMOUS)

app.register_blueprint(document_processing_bp)
app.register_blueprint(unified_processing_bp)
app.register_blueprint(enhanced_processing_bp)
app.register_blueprint(http_triggers_bp)
app.register_blueprint(queue_triggers_bp)
app.register_blueprint(document_processing_acttivity_bp)
app.register_blueprint(enchanced_extraction_activity_bp)
app.register_blueprint(progress_tracker_bp)
