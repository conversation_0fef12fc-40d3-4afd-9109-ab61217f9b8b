from dataclasses import dataclass
import logging
from typing import Callable

from fastapi import HTT<PERSON><PERSON>x<PERSON>, Request, Response, status
from fastapi.responses import JSONResponse
import httpx
from starlette.middleware.base import BaseHTTPMiddleware, _StreamingResponse

from config import settings
from constants.environment import Environment
from core import json
from exceptions import (
    ConversationDataInconsistencyError,
    DataCollectionCompleteError,
    DocumentValidationError,
    EntityNotFoundError,
    InvalidAuthentication,
    InvalidAuthorization,
    MaximumDocumentsNumberExceeded,
    MaximumDocumentsSizeExceeded,
)


__all__ = ['ExceptionToResponseMiddleware']


logger = logging.getLogger(__name__)


@dataclass
class ErrorSpecification:
    error_type: str
    description: str


class ExceptionToResponseMiddleware(BaseHTTPMiddleware):
    _STATUSCODE_SPECIFICATION_MAPPING = {
        status.HTTP_400_BAD_REQUEST: ErrorSpecification(
            'invalid_request_error ',
            'The request was malformed, missing required fields, or had invalid parameter values.',
        ),
        status.HTTP_401_UNAUTHORIZED: ErrorSpecification(
            'authentication_error', 'The API key was missing, incorrect, or invalid.'
        ),
        status.HTTP_403_FORBIDDEN: ErrorSpecification(
            'permission_error', 'The API key does not have permission to perform the requested action.'
        ),
        status.HTTP_404_NOT_FOUND: ErrorSpecification(
            'not_found_error', 'The specified model does not exist or is not available to your account'
        ),
        status.HTTP_409_CONFLICT: ErrorSpecification(
            'conflict_error', 'The request could not be completed due to a conflict (e.g., resource already exists). '
        ),
        status.HTTP_429_TOO_MANY_REQUESTS: ErrorSpecification(
            'rate_limit_error', 'You have hit a rate limit (requests per minute, tokens per minute, etc.).'
        ),
        status.HTTP_500_INTERNAL_SERVER_ERROR: ErrorSpecification(
            'service_unavailable_error', 'Server encountered an unexpected condition.'
        ),
        status.HTTP_502_BAD_GATEWAY: ErrorSpecification(
            'bad_gateway', 'Service is temporarily unavailable, often due to overload.'
        ),
        status.HTTP_503_SERVICE_UNAVAILABLE: ErrorSpecification(
            'service_unavailable_error', 'The service is temporarily unavailable (maintenance, overload, etc.). '
        ),
        status.HTTP_504_GATEWAY_TIMEOUT: ErrorSpecification('timeout', 'The request took too long to complete. '),
    }

    async def dispatch(self, request: Request, call_next: Callable) -> Response:
        try:
            response = await call_next(request)

        except HTTPException as e:
            status_code = e.status_code
            detail = e.detail

        except EntityNotFoundError as e:
            status_code = status.HTTP_404_NOT_FOUND
            detail = str(e)

        except (
            DocumentValidationError,
            MaximumDocumentsNumberExceeded,
            MaximumDocumentsSizeExceeded,
            DataCollectionCompleteError,
        ) as e:
            status_code = status.HTTP_400_BAD_REQUEST
            detail = str(e)

        except InvalidAuthentication as e:
            status_code = status.HTTP_401_UNAUTHORIZED
            detail = str(e)

        except InvalidAuthorization as e:
            status_code = status.HTTP_403_FORBIDDEN
            detail = str(e)

        except ConversationDataInconsistencyError as e:
            status_code = status.HTTP_500_INTERNAL_SERVER_ERROR  # TODO: think again concerning the status code
            detail = str(e)

        except httpx.RequestError as e:
            status_code = status.HTTP_503_SERVICE_UNAVAILABLE
            detail = f'{e}: {e.request}'

        except httpx.HTTPStatusError as e:
            status_class = e.response.status_code // 100
            status_code = status.HTTP_503_SERVICE_UNAVAILABLE if status_class in (4, 5) else e.response.status_code
            detail = str(e)

        except Exception as e:
            logger.error('', exc_info=True)
            status_code = status.HTTP_500_INTERNAL_SERVER_ERROR
            detail = str(e)

        else:
            if response.status_code < status.HTTP_400_BAD_REQUEST:
                return response

            status_code = response.status_code
            detail = await self._get_error_detail(response)

        return self._get_error_response(self._get_error_status_code(status_code), detail)

    @classmethod
    def _get_error_status_code(cls, status_code: int) -> int:
        return (
            status_code
            if (status_code < status.HTTP_400_BAD_REQUEST or status_code in cls._STATUSCODE_SPECIFICATION_MAPPING)
            else {
                4: status.HTTP_400_BAD_REQUEST,
                5: status.HTTP_500_INTERNAL_SERVER_ERROR,
            }[status_code // 100]
        )

    @staticmethod
    async def _get_error_detail(response: _StreamingResponse):
        try:
            chunks = [chunk async for chunk in response.body_iterator]
            content = b''.join(chunks).decode()  # pyright: ignore[reportArgumentType]

        except TypeError:
            logger.error('Unexpected _StreamingResponse content type')
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail='Error handling exception while decoding response',
            )

        return json.loads(content)['detail']

    @classmethod
    def _get_error_response(cls, status_code, detail: str | None):
        error_specification = cls._STATUSCODE_SPECIFICATION_MAPPING.get(
            status_code,
            cls._STATUSCODE_SPECIFICATION_MAPPING[status.HTTP_500_INTERNAL_SERVER_ERROR],
        )
        content = {
            'error_type': error_specification.error_type,
            'description': error_specification.description,
        }
        if settings.environment in (Environment.LOCAL, Environment.DEV, Environment.TEST):
            content['detail'] = detail  # pyright: ignore[reportArgumentType]
        return JSONResponse(status_code=status_code, content=content)
