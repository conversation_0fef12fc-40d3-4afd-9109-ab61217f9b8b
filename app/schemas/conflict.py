from pydantic import ConfigDict, Field

from core.schemas import CustomModel


__all__ = ['Conflict', 'LLMFieldConflictFinalized']


class Conflict(CustomModel):
    """Schema for conflict response"""

    id: int = Field(validation_alias='Id')
    field: str = Field(validation_alias='Field')
    description: str = Field(validation_alias='Description')
    conversation_id: int = Field(validation_alias='ConversationId')
    conflicting_values: list[str] = Field(validation_alias='ConflictingValues')
    chosen_value: str | None = Field(validation_alias='ChosenValue')

    model_config = ConfigDict(
        from_attributes=True,
    )


class LLMFieldConflictFinalized(CustomModel):
    sanitized_field: str
