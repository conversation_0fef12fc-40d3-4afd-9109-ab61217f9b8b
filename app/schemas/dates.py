from datetime import date

from pydantic import BaseModel, field_validator


class DatesLLMResponse(BaseModel):
    engagement_start_date: date | None = None
    engagement_end_date: date | None = None
    engagement_start_date_original: str | None = None
    engagement_end_date_original: str | None = None
    more_than_two_dates: bool | None = None

    @field_validator('engagement_start_date', 'engagement_end_date', mode='before')
    @classmethod
    def parse_date_str(cls, value: str | None) -> date | None:
        if not value or value == 'null':
            return None
        try:
            return date.fromisoformat(value)
        except ValueError:
            raise ValueError(f'{value} date must be a valid date in ISO format')
