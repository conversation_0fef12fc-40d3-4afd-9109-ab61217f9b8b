from datetime import date
from typing import Any, Literal

from pydantic import Field, field_serializer, model_validator

from constants.message import OptionType
from core import json
from core.schemas import CustomModel


__all__ = [
    'ClientNameOption',
    'ConflictResolve',
    'LDMFCountryOption',
    'KXDashTaskOption',
    'DatePickerOption',
    'Option',
]


class BaseOption(CustomModel):
    @model_validator(mode='before')
    @classmethod
    def validate_json_str(cls, value: Any) -> Any:
        if isinstance(value, str):
            try:
                result = json.loads(value)
                result['type'] = OptionType(result['type'])
                return result
            except Exception:
                raise ValueError('Invalid JSON string for option')
        return value


class ClientNameOption(BaseOption):
    type: Literal[OptionType.CLIENT_NAME] = Field(default=OptionType.CLIENT_NAME, frozen=True)
    client_name: str


class LDMFCountryOption(BaseOption):
    type: Literal[OptionType.LDMF_COUNTRY] = Field(default=OptionType.LDMF_COUNTRY, frozen=True)
    ldmf_country: str


class KXDashTaskOption(BaseOption):
    type: Literal[OptionType.KX_DASH_TASK] = Field(default=OptionType.KX_DASH_TASK, frozen=True)
    activity_id: int | None
    client_name: str | None
    engagement_code: str | None


class DatePickerOption(BaseOption):
    type: Literal[OptionType.DATES] = Field(default=OptionType.DATES, frozen=True)
    start_date: date | None
    end_date: date | None

    serialize_start_date = field_serializer('start_date')(CustomModel.serialize_date)
    serialize_end_date = field_serializer('end_date')(CustomModel.serialize_date)


class ConflictResolve(BaseOption):
    type: Literal[OptionType.CONFLICT] = Field(default=OptionType.CONFLICT, frozen=True)
    title: str
    conflict_item: str


Option = ClientNameOption | LDMFCountryOption | KXDashTaskOption | DatePickerOption | ConflictResolve
