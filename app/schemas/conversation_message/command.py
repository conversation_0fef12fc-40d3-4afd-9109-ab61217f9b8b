from typing import Any

from pydantic import model_validator

from constants.message import QualFieldName, TextEditCommand
from core import json
from core.schemas import CustomModel


__all__ = ['Command', 'SumaryResponse']


class Command(CustomModel):
    command: TextEditCommand
    field_name: QualFieldName
    context: str
    formatted_context: str
    snippet: str

    @property
    def is_undo(self) -> bool:
        return self.command == TextEditCommand.UNDO

    @property
    def is_undo_rte(self) -> bool:
        return self.is_undo and self.field_name != QualFieldName.CHAT

    @model_validator(mode='before')
    @classmethod
    def validate_json_str(cls, value: Any) -> Any:
        if isinstance(value, str):
            try:
                return json.loads(value)
            except Exception:
                raise ValueError('Invalid JSON string for command')
        return value


class SumaryResponse(CustomModel):
    value: str

    def __str__(self) -> str:
        return self.value
