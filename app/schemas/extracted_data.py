from datetime import UTC, date, datetime
import json
from typing import Any, Self
from uuid import UUID

from pydantic import ConfigDict, Field, field_validator

from constants.extracted_data import ConversationState, DataSourceType, FieldStatus, MissingDataStatus
from constants.message import SystemReplyType
from core.schemas import CustomModel
from schemas.conversation_message.option import LDMFCountryOption


__all__ = [
    'ExtractedData',
    'AggregatedData',
    'FieldHandlerResponse',
    'MissingDataResponse',
    'EngagementDates',
    'EngagementDatesOriginal',
    'IndustryData',
    'ServiceData',
]


class IndustryData(CustomModel):
    """Client industry data."""

    name: str
    ldmf_country: str
    is_primary: bool

    def __hash__(self) -> int:
        return self.name.__hash__()

    def __eq__(self, other: object) -> bool:
        if not isinstance(other, IndustryData):
            return False
        return self.name == other.name


class ServiceData(CustomModel):
    """Client service data."""

    name: str
    is_primary: bool

    def __hash__(self) -> int:
        return self.name.__hash__()

    def __eq__(self, other: object) -> bool:
        if not isinstance(other, ServiceData):
            return False
        return self.name == other.name


class ExtractedData(CustomModel):
    conversation_id: UUID = Field(alias='ConversationPublicId')
    data_source_type: DataSourceType = Field(alias='DataSourceType')
    created_at: datetime = Field(alias='CreatedAt')

    # Existing core fields
    activity_id: int | None = Field(default=None, alias='ActivityId')
    activity_name: str | None = Field(default=None, alias='ActivityName')
    client_name: list[str] | str = Field(default=[], alias='ClientName')
    ldmf_country: list[str] | str = Field(default=[], alias='LDMFCountry')
    start_date: date | None = Field(default=None, alias='StartDate')
    end_date: date | None = Field(default=None, alias='EndDate')
    start_date_original: str | None = Field(default=None, alias='StartDateOriginal')
    end_date_original: str | None = Field(default=None, alias='EndDateOriginal')
    more_than_two_dates: bool | None = Field(default=None, alias='MultipleDates')
    roles: str | None = Field(default=None, alias='Roles')
    objective_and_scope: str | None = Field(default=None, alias='ObjectiveAndScope')
    outcomes: str | None = Field(default=None, alias='Outcomes')

    # Engagement Description fields
    title: str | None = Field(default=None, alias='Title')
    business_issues: str | None = Field(default=None, alias='BusinessIssues')
    scope_approach: str | None = Field(default=None, alias='ScopeApproach')
    value_delivered: str | None = Field(default=None, alias='ValueDelivered')
    engagement_summary: str | None = Field(default=None, alias='EngagementSummary')
    one_line_description: str | None = Field(default=None, alias='OneLineDescription')

    # Engagement Details fields
    client_references: str | None = Field(default=None, alias='ClientReferences')
    client_name_sharing: str | None = Field(default=None, alias='ClientNameSharing')
    client_industries: list[IndustryData] | None = Field(default=None, alias='ClientIndustries')
    engagement_dates: str | None = Field(default=None, alias='EngagementDates')
    engagement_locations: list[str] | None = Field(default=None, alias='EngagementLocations')
    client_services: list[ServiceData] | None = Field(default=None, alias='ClientServices')
    source_of_work: str | None = Field(default=None, alias='SourceOfWork')

    # Engagement Fee fields
    engagement_fee: float | None = Field(default=None, alias='EngagementFee')
    engagement_fee_currency: str | None = Field(default=None, alias='EngagementFeeCurrency')
    is_engagement_fee_unknown: bool | None = Field(default=None, alias='IsEngagementFeeUnknown')
    is_pro_bono: bool | None = Field(default=None, alias='IsProBono')
    engagement_fee_display: str | None = Field(default=None, alias='EngagementFeeDisplay')

    # Usage & Team fields
    qual_usage: str | None = Field(default=None, alias='QualUsage')
    team_roles: str | None = Field(default=None, alias='TeamRoles')
    approver: str | None = Field(default=None, alias='Approver')

    # Debug LLM input in the second extraction stage
    other: str | None = Field(default=None, alias='Other')

    model_config = ConfigDict(
        from_attributes=True,
    )

    @field_validator('client_name', mode='before')
    @classmethod
    def validate_client_name(cls, value: str | list[str] | None) -> list[str]:
        if value is None:
            return []
        if isinstance(value, list):
            return value
        if isinstance(value, str):
            try:
                parsed = json.loads(value)
                return parsed if isinstance(parsed, list) else []
            except (json.JSONDecodeError, TypeError):
                return []
        # Handle any other type
        return []

    @field_validator('ldmf_country', mode='before')
    @classmethod
    def validate_ldmf_country(cls, value: str | list[str] | None) -> list[str]:
        if value is None:
            return []
        if isinstance(value, list):
            return value
        if isinstance(value, str):
            try:
                parsed = json.loads(value)
                return parsed if isinstance(parsed, list) else []
            except (json.JSONDecodeError, TypeError):
                return []
        # Handle any other type
        return []

    @classmethod
    def create(cls, conversation_id: UUID, data_source_type: DataSourceType) -> Self:
        return cls.model_validate(
            {
                'conversation_id': conversation_id,
                'data_source_type': data_source_type,
                'created_at': datetime.now(UTC),
            }
        )

    def model_dump_for_db(self, exclude_none: bool = False) -> dict:
        result = super().model_dump(by_alias=True, exclude_none=exclude_none)
        for field_name in ('ConversationPublicId', 'CreatedAt'):
            result.pop(field_name, None)
        for field_name in ('ClientName', 'LDMFCountry'):
            if (value := result.get(field_name)) is not None:
                result[field_name] = json.dumps(value)
        return result

    @property
    def required_fields_are_complete(self) -> bool:
        """Check if every field is not None and has values (non-empty for lists/strings)."""
        return all(
            [
                self.is_client_name_complete,
                self.is_ldmf_country_complete,
                self.is_date_interval_complete,
                self.is_objective_and_scope_complete,
                self.is_outcomes_complete,
            ]
        )

    @property
    def is_client_name_complete(self) -> bool:
        return self.client_name is not None and len(self.client_name) == 1

    @property
    def is_ldmf_country_complete(self) -> bool:
        return self.ldmf_country is not None and len(self.ldmf_country) == 1

    @property
    def is_date_interval_complete(self) -> bool:
        return (
            self.start_date is not None
            and self.end_date is not None
            and self.start_date.day > 12
            and self.end_date.day > 12
        )

    @property
    def is_objective_and_scope_complete(self) -> bool:
        return self.objective_and_scope is not None and self.objective_and_scope.strip() != ''

    @property
    def is_outcomes_complete(self) -> bool:
        return self.outcomes is not None and self.outcomes.strip() != ''


class AggregatedData(CustomModel):
    """Aggregated data from all sources."""

    # Core fields (existing)
    client_name: list[str] = []
    ldmf_country: list[str] = []
    date_intervals: list[tuple[str | None, str | None]] = []  # List of (start_date, end_date) intervals
    date_intervals_original: list[tuple[str | None, str | None]] = []  # List of (start_date, end_date) intervals
    objective_and_scope: str | None = None
    outcomes: str | None = None
    more_than_two_dates: bool | None = None

    # Engagement Description fields
    engagement_title: str | None = None
    business_issues: str | None = None
    scope_approach: str | None = None
    value_delivered: str | None = None
    engagement_summary: str | None = None
    one_line_description: str | None = None

    # Engagement Details fields
    client_references: str | None = None
    client_name_sharing: str | None = None
    client_industries: list[IndustryData] = []
    engagement_dates: list[str] = []
    engagement_locations: list[str] = []
    engagement_fee: float | None = None
    engagement_fee_currency: str | None = None
    is_engagement_fee_unknown: bool | None = None
    is_pro_bono: bool | None = None
    engagement_fee_display: str | None = None
    client_services: list[ServiceData] = []
    source_of_work: str | None = None

    # Usage & Team fields
    qual_usage: str | None = None
    team_roles: list[dict[str, Any]] | None = None
    approver: str | None = None

    @property
    def is_empty(self) -> bool:
        """Check if core fields are empty (maintains backward compatibility)."""
        return not all(
            (
                self.client_name,
                self.ldmf_country,
                self.date_intervals,
                self.objective_and_scope,
                self.outcomes,
            )
        )

    @property
    def comprehensive_fields_populated(self) -> bool:
        """Check if comprehensive fields have any data."""
        return any(
            [
                # Engagement Description fields
                self.engagement_title,
                self.business_issues,
                self.scope_approach,
                self.value_delivered,
                self.engagement_summary,
                self.one_line_description,
                # Engagement Details fields
                self.client_references,
                self.client_name_sharing,
                self.client_industries,
                self.engagement_dates,
                self.engagement_locations,
                self.engagement_fee_display,
                self.client_services,
                self.source_of_work,
                # Usage & Team fields
                self.qual_usage,
                self.team_roles,
                self.approver,
            ]
        )

    @property
    def all_fields_none(self) -> bool:
        """Return True if all fields are None or empty, False otherwise."""
        return (
            not self.client_name
            and not self.ldmf_country
            and not self.date_intervals
            and self.objective_and_scope is None
            and self.outcomes is None
        )

    @property
    def is_complete(self) -> bool:
        """Check if every field is not None and has values (non-empty for lists/strings)."""
        return all(
            [
                self.client_name is not None and len(self.client_name) == 1,
                self.ldmf_country is not None and len(self.ldmf_country) == 1,
                self.date_intervals is not None and len(self.date_intervals) == 1,
                self.objective_and_scope is not None and self.objective_and_scope.strip() != '',
                self.outcomes is not None and self.outcomes.strip() != '',
            ]
        )

    @property
    def is_client_name_complete(self) -> bool:
        return self.client_name is not None and len(self.client_name) == 1

    @property
    def is_ldmf_country_complete(self) -> bool:
        return self.ldmf_country is not None and len(self.ldmf_country) == 1

    @property
    def all_fields_are_provided(self) -> bool:
        return all(
            [
                self.client_name,
                self.ldmf_country,
                self.date_intervals,
                self.objective_and_scope,
                self.outcomes,
            ]
        )

    @property
    def is_date_unambiguous_and_complete(self) -> bool:
        """Check if the date is unambiguous and complete."""
        if not self.date_intervals:
            return False
        if len(self.date_intervals) != 1:
            return False

        # If multiple dates were detected, dates are not unambiguous
        if self.more_than_two_dates:
            return False

        start_date, end_date = self.date_intervals[0]
        if not start_date or not end_date:
            return False

        processed_start_date = date.fromisoformat(start_date)
        processed_end_date = date.fromisoformat(end_date)

        return processed_start_date.day > 12 and processed_end_date.day > 12

    @property
    def ldmf_countries_as_options(self) -> list[LDMFCountryOption]:
        return [LDMFCountryOption(ldmf_country=country) for country in self.ldmf_country]

    @property
    def detected_multiple_client_name_variations(self) -> bool:
        return len(self.client_name) > 1

    @property
    def detected_multiple_ldmf_country_variations(self) -> bool:
        return len(self.ldmf_country) > 1

    @property
    def retrieves_single_client_name(self) -> bool:
        return len(self.client_name) == 1

    @property
    def retrieves_single_ldmf_country(self) -> bool:
        return len(self.ldmf_country) == 1

    @property
    def couldnt_find_provided_client_name(self) -> bool:
        return len(self.client_name) == 0


class FieldHandlerResponse(CustomModel):
    """Response from a field handler indicating next steps."""

    needs_confirmation: bool
    system_message: str | None
    system_reply_type: SystemReplyType | None
    next_expected_field: str | None
    field_status: FieldStatus
    confirm_value: str | None = None
    options: list[str] | list[tuple[str | None, str | None]] | None = (
        None  # Options for user selection (e.g., client names)
    )


class MissingDataResponse(CustomModel):
    """Response from missing data collection indicating next steps."""

    status: MissingDataStatus
    message: str | None
    reply_type: SystemReplyType | None
    next_expected_field: str | None
    missing_fields: list[str]
    conversation_state: ConversationState
    confirm_value: str | None = None
    options: list[str] | list[tuple[str | None, str | None]] | None = (
        None  # Options for user selection (e.g., client names)
    )


class EngagementDates(CustomModel):
    start_date: date | None = None
    end_date: date | None = None


class EngagementDatesOriginal(CustomModel):
    start_date: str | None = None
    end_date: str | None = None
