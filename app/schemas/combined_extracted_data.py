from datetime import datetime
from uuid import UUID

from pydantic import Field

from core.schemas import CustomModel
from schemas.confirmed_data import ConfirmedData
from schemas.extracted_data import AggregatedData
from schemas.fee_and_currency import CurrencyItem, ProjectFeeDisplayItem
from schemas.ldmf_countries import CountryData
from schemas.project import ClientIndustryData, ClientServiceData, EngagementLocationData, SourceOfWorkData
from schemas.quals_clients import (
    ClientAPIParamsItem,
    ClientConfidentialityItem,
    ClientSearchItem,
    ClientSummaryItem,
)
from schemas.team_member import TeamMember


__all__ = [
    'CombinedExtractedDataResponse',
    'DateIntervalsObject',
]


class DateIntervalsObject(CustomModel):
    """Schema for date intervals as an object."""

    start_date: datetime | None = None
    end_date: datetime | None = None


class Fee(CustomModel):
    """Schema for fee details."""

    currency: CurrencyItem | None = None
    project_fee_display: ProjectFeeDisplayItem | None = Field(default=None, alias='projectFeeDisplay')
    value: float | None = None
    is_unknown: bool | None = Field(default=None, alias='isUnknown')
    is_pro_bono: bool | None = Field(default=None, alias='isProBono')


class CombinedExtractedDataResponse(CustomModel):
    """
    Combined schema for extracted and aggregated data.
    Flattened structure with fields from confirmed data at root level.
    """

    # Core fields from ConfirmedData
    client_name: ClientSummaryItem | None = None
    ldmf_country: CountryData | None = None
    date_intervals: DateIntervalsObject | None = None
    objective_and_scope: str | None = None
    outcomes: str | None = None

    # Dash activity ID
    dash_activity_id: int | None = Field(default=None)
    conversation_id: UUID | None = Field(default=None)

    # Fields from AggregatedData, excluding those already in ConfirmedData
    # and those explicitly excluded by the user.
    title: str | None = Field(default=None)
    business_issues: str | None = Field(default=None)
    scope_approach: str | None = Field(default=None)
    value_delivered: str | None = Field(default=None)
    engagement_summary: str | None = Field(default=None)
    one_line_description: str | None = Field(default=None)
    client_references: ClientAPIParamsItem | None = Field(default=None)
    client_name_sharing: ClientAPIParamsItem | None = Field(default=None)
    client_industries: list[ClientIndustryData] | None = Field(default=None)
    engagement_dates: list[str] = Field(default_factory=list)
    engagement_locations: list[EngagementLocationData] | None = Field(default=None)
    fee: Fee | None = Field(default=None)
    client_services: list[ClientServiceData] | None = Field(default=None)
    source_of_work: SourceOfWorkData | None = Field(default=None)
    qual_usage: str | None = Field(default=None)
    team_roles: list[TeamMember] = Field(default_factory=list)

    @classmethod
    async def from_confirmed_and_aggregated_data(
        cls,
        confirmed_data: ConfirmedData,
        aggregated_data: AggregatedData,
        conversation_id: UUID,
        dash_activity_id: int | None = None,
        ldmf_country: CountryData | None = None,
        fetched_client_name: ClientSearchItem | None = None,
        client_references: ClientAPIParamsItem | None = None,
        client_name_sharing: ClientAPIParamsItem | None = None,
        client_confidentiality: list[ClientConfidentialityItem] | None = None,
        engagement_fee_display: ProjectFeeDisplayItem | None = None,
        engagement_fee_currency: CurrencyItem | None = None,
        engagement_locations: list[EngagementLocationData] | None = None,
        client_services: list[ClientServiceData] | None = None,
        source_of_work: SourceOfWorkData | None = None,
        client_industries: list[ClientIndustryData] | None = None,
    ) -> 'CombinedExtractedDataResponse':
        # Convert date_intervals tuple to DateIntervalsObject if it exists
        date_intervals_obj = None
        summary_client = None
        if confirmed_data.date_intervals:
            start_date_str, end_date_str = confirmed_data.date_intervals
            parsed_start_date = datetime.fromisoformat(start_date_str) if start_date_str else None
            parsed_end_date = datetime.fromisoformat(end_date_str) if end_date_str else None
            date_intervals_obj = DateIntervalsObject(start_date=parsed_start_date, end_date=parsed_end_date)
        if fetched_client_name and client_confidentiality:
            c = fetched_client_name.client_confidentiality
            string_confidentiality = [item for item in client_confidentiality if item.id == c][0].name
            summary_client = ClientSummaryItem.model_validate(
                {
                    'id': fetched_client_name.id,
                    'name': fetched_client_name.name,
                    'quals_count': fetched_client_name.quals_count,
                    'client_confidentiality': string_confidentiality,
                }
            )

        fee_obj = Fee(
            currency=engagement_fee_currency,
            projectFeeDisplay=engagement_fee_display,
            value=aggregated_data.engagement_fee,
            isUnknown=aggregated_data.is_engagement_fee_unknown,
            isProBono=aggregated_data.is_pro_bono,
        )
        if not any(fee_obj.model_dump(by_alias=True).values()):
            fee_obj = None

        title = aggregated_data.engagement_title

        # Start with confirmed data fields at root level
        combined_fields = {
            'conversation_id': conversation_id,
            'client_name': summary_client,
            'ldmf_country': ldmf_country,
            'date_intervals': date_intervals_obj,
            'objective_and_scope': confirmed_data.objective_and_scope,
            'outcomes': confirmed_data.outcomes,
            'dash_activity_id': dash_activity_id,
            'client_name_sharing': client_name_sharing,
            'client_references': client_references,
            'fee': fee_obj,
            'title': title,
            'engagement_locations': engagement_locations,
            'client_services': client_services,
            'source_of_work': source_of_work,
            'client_industries': client_industries,
        }

        excluded_aggregated_fields = {
            'client_name',
            'ldmf_country',
            'date_intervals',
            'date_intervals_original',
            'objective_and_scope',
            'outcomes',
            'more_than_two_dates',
            'client_name_sharing',
            'client_references',
            'engagement_fee_display',
            'engagement_fee_currency',
            'engagement_fee',
            'is_engagement_fee_unknown',
            'is_pro_bono',
            'title',
            'engagement_locations',
            'client_services',
            'source_of_work',
            'client_industries',
        }

        # Add fields from aggregated data that are not excluded
        for field_name, value in aggregated_data.model_dump().items():
            if field_name not in excluded_aggregated_fields:
                if field_name == 'team_roles' and isinstance(value, list):
                    combined_fields[field_name] = [TeamMember.model_validate(item) for item in value]
                else:
                    combined_fields[field_name] = value

        return cls.model_validate(combined_fields)
