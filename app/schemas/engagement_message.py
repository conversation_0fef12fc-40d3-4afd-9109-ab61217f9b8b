from pydantic import Field

from constants.engagement import EngagementMessageIntention
from constants.message import MessageType
from core.schemas import CustomModel


__all__ = [
    'EngagementMessageIntentClassifierServiceResponse',
    'EngagementIntentProcessorResponse',
    'SystemMessageDetails',
]


class EngagementMessageIntentClassifierServiceResponse(CustomModel):
    """Response from the intent classifier service for engagement messages."""

    intention: EngagementMessageIntention


class EngagementIntentProcessorResponse(CustomModel):
    """Response from the engagement intent processor."""

    response: str = Field(description='The processed response based on the engagement intent.')


class SystemMessageDetails(CustomModel):
    """Details for constructing a system message."""

    content: str = Field(description='The content of the system message.')
    message_type: MessageType = Field(description='The type of the message (e.g., TEXT, ERROR).')
