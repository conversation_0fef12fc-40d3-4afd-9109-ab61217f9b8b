from pydantic import Field

from core.schemas import CustomModel


__all__ = ['EngagementFieldModificationResponse']


class EngagementFieldModificationResponse(CustomModel):
    """
    Represents the response of an engagement field modification operation.
    """

    success: bool = Field(..., description='Indicates if the modification was successful.')
    field_name: str = Field(..., description='The name of the field that was modified.')
    updated_content: str | None = Field(None, description='The new content of the modified field, if successful.')
    original_content: str | None = Field(None, description='The original content of the field before modification.')
    error: str | None = Field(None, description='Error message if the modification failed.')
