from core.enum import StrEnum


__all__ = [
    'DataSourceType',
    'RequiredField',
    'ConversationState',
    'FieldStatus',
    'MissingDataStatus',
    'ConfirmedDataFields',
    'FieldCompletionStatus',
    'ProgressStatus',
    'StrategyFactoryKey',
    'Tense',
    'QualUsage',
    'EnhancedExtractionField',
]


class DataSourceType(StrEnum):
    KX_DASH = 'kx_dash'
    DOCUMENTS = 'documents'
    PROMPT = 'prompt'


class EnhancedExtractionField(StrEnum):
    CLIENT_REFERENCES = 'client_references'
    CLIENT_NAME_SHARING = 'client_name_sharing'
    QUAL_USAGE = 'qual_usage'
    TEAM_ROLES = 'team_roles'
    ENGAGEMENT_FEE = 'engagement_fee'
    ENGAGEMENT_TITLE = 'engagement_title'
    BUSINESS_ISSUES = 'business_issues'
    SCOPE_APPROACH = 'scope_approach'
    VALUE_DELIVERED = 'value_delivered'
    ENGAGEMENT_SUMMARY = 'engagement_summary'
    ONE_LINE_DESCRIPTION = 'one_line_description'
    CLIENT_INDUSTRIES = 'client_industries'
    CLIENT_SERVICES = 'client_services'
    ENGAGEMENT_LOCATIONS = 'engagement_locations'
    SOURCE_OF_WORK = 'source_of_work'


class RequiredField(StrEnum):
    """Enum representing required fields for a qual."""

    CLIENT_NAME = 'client_name'
    LDMF_COUNTRY = 'ldmf_country'
    ENGAGEMENT_DATES = 'engagement_dates'
    OBJECTIVE_SCOPE = 'objective_scope'
    OUTCOMES = 'outcomes'

    @property
    def verbose_name(self) -> str:
        return {
            RequiredField.CLIENT_NAME: 'Client Name',
            RequiredField.LDMF_COUNTRY: 'Lead Deloitte Member Firm Country',
            RequiredField.ENGAGEMENT_DATES: 'Engagement Dates',
            RequiredField.OBJECTIVE_SCOPE: 'Objective & Scope',
            RequiredField.OUTCOMES: 'Outcomes',
        }[self]


class ConversationState(StrEnum):
    """Enum representing conversation states during qual data collection."""

    INITIAL = 'initial'
    COLLECTING_CLIENT_NAME = 'collecting_client_name'
    COLLECTING_COUNTRY = 'collecting_country'
    COLLECTING_DATES = 'collecting_dates'
    COLLECTING_OBJECTIVE = 'collecting_objective'
    COLLECTING_OUTCOMES = 'collecting_outcomes'
    COLLECTING_ADDITIONAL_DATA = 'collecting_additional_data'
    DATA_COMPLETE = 'data_complete'
    READY_FOR_QUAL_CREATION = 'ready_for_qual_creation'
    QUAL_CREATED = 'qual_created'

    @property
    def data_is_not_collected(self) -> bool:
        return self not in [
            ConversationState.DATA_COMPLETE,
            ConversationState.READY_FOR_QUAL_CREATION,
            ConversationState.QUAL_CREATED,
        ]


class FieldStatus(StrEnum):
    """Enum representing the status of a field during qual data collection."""

    MISSING = 'missing'
    SINGLE = 'single'
    MULTIPLE = 'multiple'
    CONFIRMED = 'confirmed'
    PENDING_CONFIRMATION = 'pending_confirmation'


class MissingDataStatus(StrEnum):
    """Enum representing the status of missing data during qual data collection."""

    MISSING_DATA = 'missing_data'
    DATA_COMPLETE = 'data_complete'
    ERROR = 'error'
    EXTRACTED_DATA_NOT_VALID = 'extracted_data_not_valid'
    USER_INSERTS_LDMF = 'user_inserts_ldmf'
    CLIENT_NAME_NOT_IN_API = 'client_name_not_in_api'


class ConfirmedDataFields(StrEnum):
    CLIENT_NAME = 'client_name'
    LDMF_COUNTRY = 'ldmf_country'
    DATE_INTERVALS = 'date_intervals'
    OBJECTIVE_AND_SCOPE = 'objective_and_scope'
    OUTCOMES = 'outcomes'
    PROPOSED_CLIENT_NAME = 'proposed_client_name'

    @property
    def is_prohibited_from_replacement(self) -> bool:
        return self in ConfirmedDataFields._get_fields_prohibited_from_replacement()

    @staticmethod
    def _get_fields_prohibited_from_replacement() -> set[str]:
        """
        Values in this list might be changed manually in the future, if new fields appear.
        """
        return {
            ConfirmedDataFields.CLIENT_NAME,
            ConfirmedDataFields.LDMF_COUNTRY,
            ConfirmedDataFields.OUTCOMES,
            ConfirmedDataFields.OBJECTIVE_AND_SCOPE,
        }


class FieldCompletionStatus(StrEnum):
    """Enum representing the completion status of a field."""

    MISSING = 'missing'
    PENDING_CONFIRMATION = 'pending_confirmation'
    COMPLETED = 'completed'


class ProgressStatus(StrEnum):
    """Enum representing the overall progress status."""

    INITIAL = 'initial'
    IN_PROGRESS = 'in_progress'
    COMPLETED = 'completed'


class StrategyFactoryKey(StrEnum):
    """Enum representing keys for the strategy factory."""

    MERGE = 'merge'
    DATE = 'date'
    PRIORITY = 'priority'
    CONCATENATION = 'concatenation'
    ENHANCED_MERGE = 'enhanced_merge'
    ENHANCED_PRIORITY = 'enhanced_priority'
    ENHANCED_DATE = 'enhanced_date'
    TEAM_ROLES = 'team_roles'


COMPANY_SUFFIXES = ('llc', 'inc', 'co', 'corp', 'ltd', 'limited')


class Tense(StrEnum):
    PAST = 'past'
    PRESENT = 'present'


class QualUsage(StrEnum):
    """Enum for qual usage options."""

    APPROVAL = 'approval'
    YES = 'yes'
    NO = 'no'
    DISGUISED = 'disguised'

    @property
    def description(self) -> str:
        """Get the description for this qual usage option."""
        descriptions = {
            QualUsage.APPROVAL: 'Requires partner approval for each use',
            QualUsage.YES: 'Pre-approved for full external use',
            QualUsage.NO: 'Do not use externally',
            QualUsage.DISGUISED: 'Pre-approved but must disguise client name',
        }
        return descriptions[self]

    @classmethod
    def get_description(cls, value: str) -> str | None:
        """Get description for a qual usage value."""
        for usage in cls:
            if usage.value == value:
                return usage.description
        return None

    @classmethod
    def is_valid(cls, value: str) -> bool:
        """Check if a value is a valid qual usage option."""
        return value in cls.values()


SOURCE_DATA_PROCESSING_PRIORITY = [
    DataSourceType.KX_DASH,
    DataSourceType.DOCUMENTS,
    DataSourceType.PROMPT,
]
