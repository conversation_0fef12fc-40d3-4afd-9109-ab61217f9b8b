import sqlalchemy as sa
from sqlalchemy.orm import relationship

from constants.conflict import <PERSON>Field
from core.db import Base


__all__ = ['QualConflict']


class QualConflict(Base):
    __tablename__ = 'QualConflict'

    Id = sa.Column(sa.Integer, primary_key=True, autoincrement=True)
    ConversationId = sa.Column(sa.Integer, sa.ForeignKey('QualConversation.Id'), nullable=False)

    Description = sa.Column(sa.UnicodeText, nullable=False)

    Field = sa.Column(sa.Enum(ConflictField, values_callable=lambda x: [e.value for e in x]), nullable=False)
    ConflictingValues = sa.Column(sa.JSON, nullable=False)
    ChosenValue = sa.Column(sa.UnicodeText, nullable=True, default=None)

    Conversation = relationship('QualConversation', back_populates='Conflicts')
