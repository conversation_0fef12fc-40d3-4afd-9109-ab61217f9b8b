import sqlalchemy as sa
from sqlalchemy.orm import relationship

from constants.message import QualFieldName
from core.db import Base


__all__ = ['QualFieldValue']


class QualFieldValue(Base):
    __tablename__ = 'QualFieldValue'

    Id = sa.Column(sa.Integer, primary_key=True, autoincrement=True)
    QualConversationId = sa.Column(sa.Integer, sa.<PERSON>ey('QualConversation.Id'), nullable=False)
    FieldName = sa.Column(sa.Enum(QualFieldName, values_callable=lambda x: [e.value for e in x]), nullable=False)
    FieldValue = sa.Column(sa.UnicodeText, nullable=False)
    FormattedFieldValue = sa.Column(sa.UnicodeText, nullable=False)
    CreatedAt = sa.Column(sa.DateTime, server_default=sa.func.now(), nullable=False)

    Conversation = relationship('QualConversation', back_populates='FieldValues')
