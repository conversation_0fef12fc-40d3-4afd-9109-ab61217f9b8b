["tests/repositories/test_cache.py::TestInMemoryCacheRepository::test_basic_set_and_get", "tests/repositories/test_cache.py::TestInMemoryCacheRepository::test_complex_object_serialization", "tests/repositories/test_cache.py::TestInMemoryCacheRepository::test_error_handling_in_get", "tests/repositories/test_cache.py::TestInMemoryCacheRepository::test_get_nonexistent_key", "tests/repositories/test_cache.py::TestInMemoryCacheRepository::test_key_prefix", "tests/repositories/test_cache.py::TestInMemoryCacheRepository::test_serialization_disabled", "tests/repositories/test_cache.py::TestInMemoryCacheRepository::test_serialization_error_handling", "tests/repositories/test_cache.py::TestInMemoryCacheRepository::test_ttl_expiration", "tests/services/message_handlers/test_engagement_description_handler.py::test_handle_field_modification_intent_returns_updated_qual_fields", "tests/services/message_handlers/test_prompt_handler.py::test_build_corrupted_file_error_message_multiple_files[2-corrupted_filenames0-one file-expected_prompts0]", "tests/services/message_handlers/test_prompt_handler.py::test_build_corrupted_file_error_message_multiple_files[2-corrupted_filenames2-all the files-expected_prompts2]", "tests/services/message_handlers/test_prompt_handler.py::test_build_corrupted_file_error_message_multiple_files[3-corrupted_filenames1-one file-expected_prompts1]", "tests/services/message_handlers/test_prompt_handler.py::test_build_corrupted_file_error_message_multiple_files[3-corrupted_filenames3-two files-expected_prompts3]", "tests/services/message_handlers/test_prompt_handler.py::test_build_corrupted_file_error_message_multiple_files[3-corrupted_filenames4-all the files-expected_prompts4]", "tests/services/message_handlers/test_prompt_handler.py::test_build_corrupted_file_error_message_multiple_files[4-corrupted_filenames5-all the files-expected_prompts5]", "tests/services/message_handlers/test_prompt_handler.py::test_build_corrupted_file_error_message_single_file_corrupted", "tests/services/test_extracted_data_service.py::TestExtractedDataService::test_mask_client_name_in_response_no_client_name", "tests/services/test_extracted_data_service.py::TestExtractedDataService::test_mask_client_name_in_response_some_fields_are_none", "tests/services/test_extracted_data_service.py::TestExtractedDataService::test_mask_client_name_in_response_success", "tests/services/test_extracted_data_service.py::TestExtractedDataService::test_update_masked_data_success", "tests/services/test_extracted_data_service_teams_roles.py::test_roles_and_services_aggregation_with_mocked_data", "tests/services/test_extracted_data_service_teams_roles.py::test_team_roles_aggregation_with_mocked_team_members", "tests/services/test_message_processor.py::TestMessageProcessor::test_handle_client_name_input_too_long", "tests/services/test_message_processor.py::TestMessageProcessor::test_handle_client_name_input_valid", "tests/services/test_message_processor.py::TestMessageProcessor::test_orchestrator_integration_extraction_intent", "tests/services/test_message_processor.py::TestMessageProcessor::test_orchestrator_integration_undefined_intent", "tests/test_cache_integration.py::TestLDMFCountryServiceCacheIntegration::test_cache_hit", "tests/test_cache_integration.py::TestLDMFCountryServiceCacheIntegration::test_cache_key_consistency", "tests/test_cache_integration.py::TestLDMFCountryServiceCacheIntegration::test_cache_miss_and_set", "tests/test_cache_integration.py::TestLDMFCountryServiceCacheIntegration::test_error_handling_with_cache", "tests/test_cache_integration.py::TestQualsClientsRepositoryCacheIntegration::test_get_client_sharing_list_cache_hit", "tests/test_cache_integration.py::TestQualsClientsRepositoryCacheIntegration::test_get_client_sharing_list_cache_miss", "tests/test_cache_integration.py::TestQualsClientsRepositoryCacheIntegration::test_get_references_list_cache_hit", "tests/test_cache_integration.py::TestQualsClientsRepositoryCacheIntegration::test_get_references_list_cache_miss", "tests/test_cache_integration.py::TestQualsClientsRepositoryCacheIntegration::test_multiple_cache_keys", "tests/test_chat_undo_functionality.py::TestChatUndoFunctionality::test_chat_undo_with_dependent_field_regeneration", "tests/test_chat_undo_functionality.py::TestChatUndoFunctionality::test_chat_undo_with_no_recent_changes", "tests/test_chat_undo_functionality.py::TestChatUndoFunctionality::test_chat_undo_with_regenerator_field_change", "tests/test_chat_undo_functionality.py::TestChatUndoFunctionality::test_chat_undo_with_regular_field_change", "tests/test_client_creation_flow.py::TestClientCreationFlow::test_client_creation_alternative_name_flow", "tests/test_client_creation_flow.py::TestClientCreationFlow::test_client_creation_confirmation_flow", "tests/test_client_name_confirmation_bug_fix.py::TestClientNameConfirmationBugFix::test_client_name_confirmation_uses_extracted_name_not_full_prompt[YES, this is correct-MSD International GmbH (Singapore Branch)]", "tests/test_client_name_confirmation_bug_fix.py::TestClientNameConfirmationBugFix::test_client_name_confirmation_uses_extracted_name_not_full_prompt[Yes, this is correct-MSD International GmbH (Singapore Branch)]", "tests/test_client_name_confirmation_bug_fix.py::TestClientNameConfirmationBugFix::test_client_name_confirmation_uses_extracted_name_not_full_prompt[Yes-MSD International GmbH (Singapore Branch)]", "tests/test_client_name_confirmation_bug_fix.py::TestClientNameConfirmationBugFix::test_client_name_confirmation_uses_extracted_name_not_full_prompt[yES-MSD International GmbH (Singapore Branch)]", "tests/test_client_name_confirmation_bug_fix.py::TestClientNameConfirmationBugFix::test_client_name_confirmation_uses_extracted_name_not_full_prompt[yes, this is correct-MSD International GmbH (Singapore Branch)]", "tests/test_client_name_confirmation_bug_fix.py::TestClientNameConfirmationBugFix::test_client_name_confirmation_uses_extracted_name_not_full_prompt[yes-MSD International GmbH (Singapore Branch)]", "tests/test_client_name_confirmation_bug_fix.py::TestClientNameConfirmationBugFix::test_manual_client_name_entry_still_works", "tests/test_client_name_detection.py::TestClientNameDetection::test_no_client_match_requires_confirmation", "tests/test_client_name_detection.py::TestClientNameDetection::test_single_client_match_auto_confirmation", "tests/test_client_name_detection.py::TestClientNameDetection::test_single_client_match_requires_confirmation", "tests/test_client_name_detection.py::TestClientNameDetectionPatterns::test_user_message_is_none", "tests/test_client_name_handler.py::TestClientNameHandler::test_api_error_fallback", "tests/test_client_name_handler.py::TestClientNameHandler::test_api_error_fallback_single_client_exception", "tests/test_client_name_handler.py::TestClientNameHandler::test_client_name_already_confirmed", "tests/test_client_name_handler.py::TestClientNameHandler::test_multiple_client_names_with_api_success", "tests/test_client_name_handler.py::TestClientNameHandler::test_multiple_client_names_without_api", "tests/test_client_name_handler.py::TestClientNameHandler::test_no_client_names_found", "tests/test_client_name_handler.py::TestClientNameHandler::test_single_client_name_needs_confirmation", "tests/test_command_functionality.py::TestCommandAPIIntegration::test_command_processing_through_api[expand-business_issues-field_saved]", "tests/test_command_functionality.py::TestCommandAPIIntegration::test_command_processing_through_api[prompt-business_issues-field_saved]", "tests/test_command_functionality.py::TestCommandAPIIntegration::test_command_processing_through_api[rewrite-scope_approach-field_saved]", "tests/test_command_functionality.py::TestCommandAPIIntegration::test_command_processing_through_api[shorten-value_delivered-field_saved]", "tests/test_command_functionality.py::TestCommandAPIIntegration::test_malformed_command_json_format", "tests/test_command_functionality.py::TestCommandAPIIntegration::test_missing_required_command_fields", "tests/test_command_functionality.py::TestCommandJSONValidation::test_command_dict_validation_success", "tests/test_command_functionality.py::TestCommandJSONValidation::test_command_invalid_enum_values", "tests/test_command_functionality.py::TestCommandJSONValidation::test_command_invalid_json_string", "tests/test_command_functionality.py::TestCommandJSONValidation::test_command_json_string_validation_success", "tests/test_command_functionality.py::TestCommandJSONValidation::test_command_missing_required_fields", "tests/test_command_functionality.py::TestCommandService::test_command_service_handle_success", "tests/test_command_functionality.py::TestCommandService::test_command_service_preserves_original_command_properties", "tests/test_command_functionality.py::TestCommandService::test_handle_client_name_replacement_no_client_name_in_fields", "tests/test_command_functionality.py::TestCommandService::test_handle_client_name_replacement_some_fields_are_none", "tests/test_command_functionality.py::TestCommandService::test_handle_client_name_replacement_success", "tests/test_command_functionality.py::TestCommandService::test_process_command_sets_is_undoable_flag[expand-True]", "tests/test_command_functionality.py::TestCommandService::test_process_command_sets_is_undoable_flag[prompt-True]", "tests/test_command_functionality.py::TestCommandService::test_process_command_sets_is_undoable_flag[rewrite-True]", "tests/test_command_functionality.py::TestCommandService::test_process_command_sets_is_undoable_flag[shorten-True]", "tests/test_command_functionality.py::TestCommandService::test_process_command_sets_is_undoable_flag[store-False]", "tests/test_command_functionality.py::TestCommandService::test_undo_command_sets_is_undoable_to_false", "tests/test_command_functionality.py::TestCommandService::test_undo_field_change_no_previous_value", "tests/test_command_functionality.py::TestPromptTextEditFunctionality::test_non_prompt_command_excludes_user_request", "tests/test_command_functionality.py::TestPromptTextEditFunctionality::test_prompt_command_api_integration", "tests/test_command_functionality.py::TestPromptTextEditFunctionality::test_prompt_command_includes_user_request_in_data_to_analyze", "tests/test_command_functionality.py::TestPromptTextEditFunctionality::test_prompt_command_snippet_not_found_handling", "tests/test_command_functionality.py::TestPromptTextEditFunctionality::test_prompt_command_with_empty_user_content", "tests/test_command_functionality.py::TestPromptTextEditFunctionality::test_user_request_inclusion_by_command_type[expand-False]", "tests/test_command_functionality.py::TestPromptTextEditFunctionality::test_user_request_inclusion_by_command_type[prompt-True]", "tests/test_command_functionality.py::TestPromptTextEditFunctionality::test_user_request_inclusion_by_command_type[rewrite-False]", "tests/test_command_functionality.py::TestPromptTextEditFunctionality::test_user_request_inclusion_by_command_type[shorten-False]", "tests/test_command_functionality.py::TestPromptTextEditFunctionality::test_user_request_inclusion_by_command_type[store-False]", "tests/test_command_functionality.py::TestTextEditService::test_text_edit_service_different_commands[expand-expanded and detailed business context with additional information]", "tests/test_command_functionality.py::TestTextEditService::test_text_edit_service_different_commands[rewrite-completely rewritten business context with new phrasing]", "tests/test_command_functionality.py::TestTextEditService::test_text_edit_service_different_commands[shorten-concise business context]", "tests/test_command_functionality.py::TestTextEditService::test_text_edit_service_snippet_not_found", "tests/test_command_functionality.py::TestTextEditService::test_text_edit_service_text_splitting", "tests/test_conditional_field_regeneration.py::TestConditionalFieldRegeneration::test_conditional_regeneration_error_handling", "tests/test_conditional_field_regeneration.py::TestConditionalFieldRegeneration::test_conditional_regeneration_excludes_undo_operations", "tests/test_conditional_field_regeneration.py::TestConditionalFieldRegeneration::test_conditional_regeneration_includes_undo_operations", "tests/test_conditional_field_regeneration.py::TestConditionalFieldRegeneration::test_conditional_regeneration_integration_with_existing_fields", "tests/test_conditional_field_regeneration.py::TestConditionalFieldRegeneration::test_conditional_regeneration_trigger_conditions[expand-business_issues-True]", "tests/test_conditional_field_regeneration.py::TestConditionalFieldRegeneration::test_conditional_regeneration_trigger_conditions[expand-engagement_summary-False]", "tests/test_conditional_field_regeneration.py::TestConditionalFieldRegeneration::test_conditional_regeneration_trigger_conditions[expand-one_line_description-False]", "tests/test_conditional_field_regeneration.py::TestConditionalFieldRegeneration::test_conditional_regeneration_trigger_conditions[expand-undefined-False]", "tests/test_conditional_field_regeneration.py::TestConditionalFieldRegeneration::test_conditional_regeneration_trigger_conditions[prompt-business_issues-True]", "tests/test_conditional_field_regeneration.py::TestConditionalFieldRegeneration::test_conditional_regeneration_trigger_conditions[rewrite-value_delivered_impact-True]", "tests/test_conditional_field_regeneration.py::TestConditionalFieldRegeneration::test_conditional_regeneration_trigger_conditions[shorten-scope_approach-True]", "tests/test_conditional_field_regeneration.py::TestConditionalFieldRegeneration::test_conditional_regeneration_trigger_conditions[store-business_issues-False]", "tests/test_conditional_field_regeneration.py::TestConditionalFieldRegeneration::test_conditional_regeneration_trigger_conditions[undo-business_issues-False]", "tests/test_conversation_endpoints.py::test_create_conversation_success", "tests/test_conversation_endpoints.py::test_create_conversation_validation_errors[invalid_data0]", "tests/test_conversation_endpoints.py::test_create_conversation_validation_errors[invalid_data1]", "tests/test_conversation_endpoints.py::test_create_conversation_wrong_authentication[headers0-Authorization header is missing or empty]", "tests/test_conversation_endpoints.py::test_create_conversation_wrong_authentication[headers1-Wrong authorization scheme]", "tests/test_conversation_endpoints.py::test_create_conversation_wrong_authentication[headers2-Token is missing]", "tests/test_conversation_endpoints.py::test_create_conversation_wrong_authentication_token_scopes[mocked_scp0-Malformed scopes]", "tests/test_conversation_endpoints.py::test_create_conversation_wrong_authentication_token_scopes[mocked_scp1-Missing a required scope]", "tests/test_conversation_endpoints.py::test_delete_all_conversation_messages_wrong_user_id", "tests/test_conversation_endpoints.py::test_delete_conversation_basic_functionality", "tests/test_conversation_endpoints.py::test_delete_conversation_deletes_all_messages", "tests/test_conversation_endpoints.py::test_delete_conversation_invalid_uuid", "tests/test_conversation_endpoints.py::test_delete_conversation_not_found", "tests/test_conversation_endpoints.py::test_get_combined_extracted_data", "tests/test_conversation_endpoints.py::test_get_combined_extracted_data_qual_usage_values[approval]", "tests/test_conversation_endpoints.py::test_get_combined_extracted_data_qual_usage_values[disguised]", "tests/test_conversation_endpoints.py::test_get_combined_extracted_data_qual_usage_values[no]", "tests/test_conversation_endpoints.py::test_get_combined_extracted_data_qual_usage_values[undefined]", "tests/test_conversation_endpoints.py::test_get_combined_extracted_data_qual_usage_values[yes]", "tests/test_conversation_endpoints.py::test_get_conversation_extra_data", "tests/test_conversation_endpoints.py::test_get_conversation_invalid_user_uuid", "tests/test_conversation_endpoints.py::test_get_conversation_invalid_uuid", "tests/test_conversation_endpoints.py::test_get_conversation_last_message", "tests/test_conversation_endpoints.py::test_get_conversation_last_message_not_found", "tests/test_conversation_endpoints.py::test_get_conversation_messages_malformed_uuid", "tests/test_conversation_endpoints.py::test_get_conversation_messages_not_found", "tests/test_conversation_endpoints.py::test_get_conversation_messages_success", "tests/test_conversation_endpoints.py::test_get_conversation_messages_with_page_type_filter", "tests/test_conversation_endpoints.py::test_get_conversation_messages_wrong_user_id", "tests/test_conversation_endpoints.py::test_get_conversation_not_found", "tests/test_conversation_endpoints.py::test_get_conversation_success", "tests/test_conversation_endpoints.py::test_get_suggested_quals_conditional_logic", "tests/test_conversation_endpoints.py::test_start_engagement_descriptions_chat_with_page_type", "tests/test_conversation_message_repository.py::TestConversationMessageRepositoryUpdate::test_disable_undoable_flags", "tests/test_conversation_message_repository.py::TestConversationMessageRepositoryUpdate::test_update_content_nonexistent_message", "tests/test_conversation_message_repository.py::TestConversationMessageRepositoryUpdate::test_update_content_preserves_other_fields", "tests/test_conversation_message_repository.py::TestConversationMessageRepositoryUpdate::test_update_content_success", "tests/test_conversation_message_repository.py::TestExtractedDataMessageFormatting::test_format_extracted_data_message_complete_data", "tests/test_conversation_message_repository.py::TestExtractedDataMessageFormatting::test_format_extracted_data_message_empty_data", "tests/test_conversation_message_repository.py::TestExtractedDataMessageFormatting::test_format_extracted_data_message_partial_data", "tests/test_country_handler_integration.py::TestErrorHandlingAndEdgeCases::test_malformed_confirmed_data_json", "tests/test_country_handler_integration.py::TestErrorHandlingAndEdgeCases::test_nonexistent_conversation_id", "tests/test_country_handler_integration.py::TestExtractedDataServiceCountryIntegration::test_missing_data_detection_country_confirmed", "tests/test_country_handler_integration.py::TestExtractedDataServiceCountryIntegration::test_missing_data_detection_country_in_aggregated_data", "tests/test_country_handler_integration.py::TestExtractedDataServiceCountryIntegration::test_missing_data_detection_country_missing", "tests/test_country_handler_integration.py::TestLDMFCountryHandlerUnit::test_confirmed_data_overrides_aggregated_data", "tests/test_country_handler_integration.py::TestLDMFCountryHandlerUnit::test_country_already_confirmed", "tests/test_country_handler_integration.py::TestLDMFCountryHandlerUnit::test_country_in_aggregated_data_not_confirmed", "tests/test_country_handler_integration.py::TestLDMFCountryHandlerUnit::test_country_missing_entirely", "tests/test_country_handler_integration.py::TestThreeScenarioCountryHandling::test_multiple_values_scenario", "tests/test_country_handler_integration.py::TestThreeScenarioCountryHandling::test_one_value_scenario", "tests/test_country_handler_integration.py::TestThreeScenarioCountryHandling::test_zero_values_scenario", "tests/test_data_collection_validation.py::TestDataCollectionValidation::test_get_combined_extracted_data_succeeds_when_data_collection_complete[data_complete]", "tests/test_data_collection_validation.py::TestDataCollectionValidation::test_get_combined_extracted_data_succeeds_when_data_collection_complete[qual_created]", "tests/test_data_collection_validation.py::TestDataCollectionValidation::test_get_combined_extracted_data_succeeds_when_data_collection_complete[ready_for_qual_creation]", "tests/test_data_collection_validation.py::TestDataCollectionValidation::test_start_assistant_mode_succeeds_when_data_collection_complete[data_complete]", "tests/test_data_collection_validation.py::TestDataCollectionValidation::test_start_assistant_mode_succeeds_when_data_collection_complete[qual_created]", "tests/test_data_collection_validation.py::TestDataCollectionValidation::test_start_assistant_mode_succeeds_when_data_collection_complete[ready_for_qual_creation]", "tests/test_data_collection_validation.py::TestDataCollectionValidation::test_update_qual_id_raises_error_when_data_collection_not_complete[collecting_additional_data]", "tests/test_data_collection_validation.py::TestDataCollectionValidation::test_update_qual_id_raises_error_when_data_collection_not_complete[collecting_client_name]", "tests/test_data_collection_validation.py::TestDataCollectionValidation::test_update_qual_id_raises_error_when_data_collection_not_complete[collecting_country]", "tests/test_data_collection_validation.py::TestDataCollectionValidation::test_update_qual_id_raises_error_when_data_collection_not_complete[collecting_dates]", "tests/test_data_collection_validation.py::TestDataCollectionValidation::test_update_qual_id_raises_error_when_data_collection_not_complete[collecting_objective]", "tests/test_data_collection_validation.py::TestDataCollectionValidation::test_update_qual_id_raises_error_when_data_collection_not_complete[collecting_outcomes]", "tests/test_data_collection_validation.py::TestDataCollectionValidation::test_update_qual_id_raises_error_when_data_collection_not_complete[initial]", "tests/test_data_collection_validation.py::TestDataCollectionValidation::test_update_qual_id_succeeds_when_data_collection_complete[data_complete]", "tests/test_data_collection_validation.py::TestDataCollectionValidation::test_update_qual_id_succeeds_when_data_collection_complete[qual_created]", "tests/test_data_collection_validation.py::TestDataCollectionValidation::test_update_qual_id_succeeds_when_data_collection_complete[ready_for_qual_creation]", "tests/test_data_complete_flow.py::TestDataCompleteFlow::test_client_name_fallback_when_none", "tests/test_data_complete_flow.py::TestDataCompleteFlow::test_data_complete_shows_confirmed_fields_ready", "tests/test_data_complete_flow.py::TestDataCompleteFlow::test_user_confirms_ready_to_create_qual", "tests/test_data_complete_flow.py::TestDataCompleteFlow::test_user_wants_to_add_more_info", "tests/test_data_complete_flow.py::TestDataCompleteFlow::test_various_confirmation_responses[create my qual]", "tests/test_data_complete_flow.py::TestDataCompleteFlow::test_various_confirmation_responses[generate the qual]", "tests/test_data_complete_flow.py::TestDataCompleteFlow::test_various_confirmation_responses[no]", "tests/test_data_complete_flow.py::TestDataCompleteFlow::test_various_confirmation_responses[proceed with creation]", "tests/test_data_complete_flow.py::TestDataCompleteFlow::test_various_confirmation_responses[ready to create]", "tests/test_document_description_formatting.py::TestDocumentDescriptionFormatting::test_format_document_description_empty_list", "tests/test_document_description_formatting.py::TestDocumentDescriptionFormatting::test_format_document_description_four_files", "tests/test_document_description_formatting.py::TestDocumentDescriptionFormatting::test_format_document_description_removes_extensions", "tests/test_document_description_formatting.py::TestDocumentDescriptionFormatting::test_format_document_description_single_file", "tests/test_document_description_formatting.py::TestDocumentDescriptionFormatting::test_format_document_description_three_files", "tests/test_document_description_formatting.py::TestDocumentDescriptionFormatting::test_format_document_description_two_files", "tests/test_document_description_formatting.py::TestDocumentDescriptionFormatting::test_generate_client_confirmation_content_multiple_documents", "tests/test_document_description_formatting.py::TestDocumentDescriptionFormatting::test_generate_client_confirmation_content_no_documents", "tests/test_document_description_formatting.py::TestDocumentDescriptionFormatting::test_generate_client_confirmation_content_single_document", "tests/test_document_queue.py::test_document_queue_initialise", "tests/test_document_queue.py::test_send_unified_message_with_both_text_and_documents", "tests/test_document_queue.py::test_send_unified_message_with_documents", "tests/test_document_queue.py::test_send_unified_message_with_text_prompt", "tests/test_document_upload.py::test_delete_conversation_removes_documents", "tests/test_document_upload.py::test_delete_conversation_wrong_doc_blob_path", "tests/test_document_upload.py::test_upload_documents_empty_filename", "tests/test_document_upload.py::test_upload_documents_file_too_large", "tests/test_document_upload.py::test_upload_documents_invalid_file_type", "tests/test_document_upload.py::test_upload_documents_max_file_size_with_buffer", "tests/test_document_upload.py::test_upload_documents_nonexistent_conversation", "tests/test_document_upload.py::test_upload_documents_success", "tests/test_document_upload.py::test_upload_documents_too_many_files", "tests/test_document_upload.py::test_upload_valid_document_after_corrupted", "tests/test_engagement_chats_endpoints.py::test_start_new_message_chat_with_page_type[engagement_description]", "tests/test_engagement_chats_endpoints.py::test_start_new_message_chat_with_page_type[engagement_details]", "tests/test_engagement_chats_endpoints.py::test_start_new_message_chat_with_page_type[usage_and_team_details]", "tests/test_engagement_field_modification_integration.py::TestEngagementDescriptionCommandIntegrationWithFieldModification::test_command_processing_vs_field_modification_coexistence", "tests/test_engagement_field_modification_integration.py::TestEngagementDescriptionCommandIntegrationWithFieldModification::test_field_modification_and_command_affect_different_data_stores", "tests/test_engagement_field_modification_integration.py::TestEngagementFieldAggregation::test_engagement_title_aggregation_from_multiple_sources", "tests/test_engagement_field_modification_integration.py::TestEngagementFieldModificationIntegration::test_business_issues_field_modification_complete_flow", "tests/test_engagement_field_modification_integration.py::TestEngagementFieldModificationIntegration::test_engagement_title_field_modification_complete_flow", "tests/test_engagement_field_modification_integration.py::TestEngagementFieldModificationIntegration::test_multiple_engagement_fields_modification", "tests/test_engagement_field_modification_integration.py::TestEngagementFieldModificationIntegration::test_non_field_modification_intent_fallback", "tests/test_engagement_field_modification_integration.py::TestEngagementFieldModificationIntegration::test_openai_api_failure_handling", "tests/test_engagement_field_modification_integration.py::TestEngagementFieldModificationService::test_field_modification_with_empty_original_content", "tests/test_engagement_field_modification_integration.py::TestEngagementFieldModificationService::test_field_modification_with_existing_data", "tests/test_engagement_field_modification_integration.py::TestEngagementFieldModificationService::test_unsupported_intent_raises_error", "tests/test_extracted_data_handlers.py::TestClientNameHandler::test_client_name_already_confirmed", "tests/test_extracted_data_handlers.py::TestClientNameHandler::test_client_name_contains_and_api_exact_match", "tests/test_extracted_data_handlers.py::TestClientNameHandler::test_client_name_contains_and_api_no_matches", "tests/test_extracted_data_handlers.py::TestClientNameHandler::test_client_name_contains_and_api_no_matches_company_suffix_in_name[parametrized_data0]", "tests/test_extracted_data_handlers.py::TestClientNameHandler::test_client_name_contains_and_api_no_matches_company_suffix_in_name[parametrized_data1]", "tests/test_extracted_data_handlers.py::TestClientNameHandler::test_client_name_contains_and_api_no_matches_company_suffix_in_name[parametrized_data2]", "tests/test_extracted_data_handlers.py::TestClientNameHandler::test_client_name_contains_and_api_no_matches_company_suffix_in_name[parametrized_data3]", "tests/test_extracted_data_handlers.py::TestClientNameHandler::test_client_name_contains_and_api_no_matches_company_suffix_in_name[parametrized_data4]", "tests/test_extracted_data_handlers.py::TestClientNameHandler::test_client_name_no_contains_and", "tests/test_extracted_data_handlers.py::TestClientNameHandler::test_multiple_client_names_always_returns_multiple_options_regardless_of_api_results", "tests/test_extracted_data_handlers.py::TestClientNameHandler::test_multiple_client_names_in_aggregated_data_api_fails", "tests/test_extracted_data_handlers.py::TestClientNameHandler::test_multiple_client_names_in_aggregated_data_found_in_api", "tests/test_extracted_data_handlers.py::TestClientNameHandler::test_multiple_client_names_in_aggregated_data_single_match_from_api", "tests/test_extracted_data_handlers.py::TestClientNameHandler::test_no_client_names_in_aggregated_data", "tests/test_extracted_data_handlers.py::TestClientNameHandler::test_single_client_name_api_exact_match", "tests/test_extracted_data_handlers.py::TestClientNameHandler::test_single_client_name_api_fails", "tests/test_extracted_data_handlers.py::TestClientNameHandler::test_single_client_name_api_multiple_matches", "tests/test_extracted_data_handlers.py::TestClientNameHandler::test_single_client_name_api_no_matches", "tests/test_extracted_data_handlers.py::TestDateIntervalsHandler::test_date_intervals_already_confirmed", "tests/test_extracted_data_handlers.py::TestDateIntervalsHandler::test_date_intervals_in_aggregated_data_with_both_dates", "tests/test_extracted_data_handlers.py::TestDateIntervalsHandler::test_date_intervals_in_aggregated_data_with_end_date_only", "tests/test_extracted_data_handlers.py::TestDateIntervalsHandler::test_date_intervals_in_aggregated_data_with_start_date_only", "tests/test_extracted_data_handlers.py::TestDateIntervalsHandler::test_no_date_intervals_in_aggregated_data", "tests/test_extracted_data_handlers.py::TestLDMFCountryHandler::test_ldmf_country_already_confirmed", "tests/test_extracted_data_handlers.py::TestLDMFCountryHandler::test_ldmf_country_fetch_exception", "tests/test_extracted_data_handlers.py::TestLDMFCountryHandler::test_ldmf_country_handler_returns_missing", "tests/test_extracted_data_handlers.py::TestLDMFCountryHandler::test_multiple_invalid_countries", "tests/test_extracted_data_handlers.py::TestLDMFCountryHandler::test_multiple_mixed_countries", "tests/test_extracted_data_handlers.py::TestLDMFCountryHandler::test_multiple_valid_countries", "tests/test_extracted_data_handlers.py::TestLDMFCountryHandler::test_single_country_with_empty_verification", "tests/test_extracted_data_handlers.py::TestLDMFCountryHandler::test_single_country_with_multiple_matches", "tests/test_extracted_data_handlers.py::TestLDMFCountryHandler::test_single_invalid_country", "tests/test_extracted_data_handlers.py::TestLDMFCountryHandler::test_single_valid_country", "tests/test_extracted_data_handlers.py::TestObjectiveHandler::test_objective_already_confirmed", "tests/test_extracted_data_handlers.py::TestObjectiveHandler::test_objective_in_aggregated_data", "tests/test_extracted_data_handlers.py::TestObjectiveHandler::test_objective_missing", "tests/test_extracted_data_handlers.py::TestOutcomesHandler::test_outcomes_already_confirmed", "tests/test_extracted_data_handlers.py::TestOutcomesHandler::test_outcomes_in_aggregated_data", "tests/test_extracted_data_handlers.py::TestOutcomesHandler::test_outcomes_missing", "tests/test_extracted_data_repository_update_fix.py::TestExtractedDataRepositoryUpdateFix::test_empty_list_fields_dont_overwrite_existing", "tests/test_extracted_data_repository_update_fix.py::TestExtractedDataRepositoryUpdateFix::test_empty_string_fields_dont_overwrite_existing", "tests/test_extracted_data_repository_update_fix.py::TestExtractedDataRepositoryUpdateFix::test_meaningful_values_still_update_existing", "tests/test_extracted_data_repository_update_fix.py::TestExtractedDataRepositoryUpdateFix::test_none_dates_dont_overwrite_existing_dates", "tests/test_extracted_data_repository_update_fix.py::TestExtractedDataRepositoryUpdateFix::test_partial_extraction_preserves_existing_data", "tests/test_extracted_data_service.py::TestConfirmedData::test_from_json_string_with_invalid_json_returns_empty", "tests/test_extracted_data_service.py::TestConfirmedData::test_from_json_string_with_none_returns_empty", "tests/test_extracted_data_service.py::TestConfirmedData::test_from_json_string_with_valid_json", "tests/test_extracted_data_service.py::TestConfirmedData::test_get_current_conversation_state[fields0-collecting_client_name]", "tests/test_extracted_data_service.py::TestConfirmedData::test_get_current_conversation_state[fields1-collecting_country]", "tests/test_extracted_data_service.py::TestConfirmedData::test_get_current_conversation_state[fields2-collecting_dates]", "tests/test_extracted_data_service.py::TestConfirmedData::test_get_current_conversation_state[fields3-collecting_objective]", "tests/test_extracted_data_service.py::TestConfirmedData::test_get_current_conversation_state[fields4-collecting_outcomes]", "tests/test_extracted_data_service.py::TestConfirmedData::test_get_current_conversation_state[fields5-data_complete]", "tests/test_extracted_data_service.py::TestConfirmedData::test_is_empty_false", "tests/test_extracted_data_service.py::TestConfirmedData::test_is_empty_true", "tests/test_extracted_data_service.py::TestConfirmedData::test_required_fields_are_complete_false", "tests/test_extracted_data_service.py::TestConfirmedData::test_required_fields_are_complete_true", "tests/test_extracted_data_service.py::TestConfirmedData::test_tense_type[None-past]", "tests/test_extracted_data_service.py::TestConfirmedData::test_tense_type[date_intervals1-past]", "tests/test_extracted_data_service.py::TestConfirmedData::test_tense_type[date_intervals2-past]", "tests/test_extracted_data_service.py::TestConfirmedData::test_tense_type[date_intervals3-present]", "tests/test_extracted_data_service.py::TestConfirmedData::test_to_json_string_excludes_none_fields", "tests/test_extracted_data_service.py::TestExtractedDataService::test_aggregate_data_fills_date_gap_from_following_source[parametrized_data0]", "tests/test_extracted_data_service.py::TestExtractedDataService::test_aggregate_data_fills_date_gap_from_following_source[parametrized_data1]", "tests/test_extracted_data_service.py::TestExtractedDataService::test_aggregate_data_fills_date_gap_from_following_source[parametrized_data2]", "tests/test_extracted_data_service.py::TestExtractedDataService::test_aggregate_data_fills_missing_prompt_fields_from_other_sources", "tests/test_extracted_data_service.py::TestExtractedDataService::test_aggregate_data_prioritizes_sources_correctly", "tests/test_extracted_data_service.py::TestExtractedDataService::test_aggregate_data_uses_other_sources_when_prompt_empty", "tests/test_extracted_data_service.py::TestExtractedDataService::test_extracted_data_schema", "tests/test_extracted_data_service_blob_integration.py::TestDocumentProcessingIntegration::test_document_extraction_error_handling", "tests/test_extracted_data_service_blob_integration.py::TestDocumentProcessingIntegration::test_document_upload_and_extraction_flow", "tests/test_extracted_data_service_blob_integration.py::TestDocumentProcessingIntegration::test_multiple_document_sources_aggregation", "tests/test_extracted_data_service_blob_integration.py::TestDurableFunctionsIntegration::test_document_processing_workflow_simulation", "tests/test_extracted_data_service_blob_integration.py::TestDurableFunctionsIntegration::test_prompt_processing_workflow_simulation", "tests/test_extracted_data_service_connection_error.py::TestExtractedDataServiceConnectionError::test_process_activity_data_handles_connection_errors", "tests/test_extracted_data_service_connection_error.py::TestExtractedDataServiceConnectionError::test_process_activity_data_handles_partial_failures", "tests/test_extracted_data_service_connection_error.py::TestExtractedDataServiceConnectionError::test_update_handles_connection_errors_in_process_activity_data", "tests/test_field_value_command_integration.py::TestFieldValueCommandIntegration::test_command_creates_field_value_record", "tests/test_field_value_command_integration.py::TestFieldValueCommandIntegration::test_command_error_handling_text_edit_failure", "tests/test_field_value_command_integration.py::TestFieldValueCommandIntegration::test_command_processing_different_field_values[business_issues-Initial business issues context]", "tests/test_field_value_command_integration.py::TestFieldValueCommandIntegration::test_command_processing_different_field_values[engagement_summary-Initial engagement summary context]", "tests/test_field_value_command_integration.py::TestFieldValueCommandIntegration::test_command_processing_different_field_values[one_line_description-Initial one line description]", "tests/test_field_value_command_integration.py::TestFieldValueCommandIntegration::test_command_processing_different_field_values[scope_approach-Initial scope and approach context]", "tests/test_field_value_command_integration.py::TestFieldValueCommandIntegration::test_command_processing_different_field_values[value_delivered-Initial value delivered context]", "tests/test_field_value_command_integration.py::TestFieldValueCommandIntegration::test_command_with_existing_extracted_data", "tests/test_field_value_command_integration.py::TestFieldValueCommandIntegration::test_command_with_non_field_value_page_type", "tests/test_field_value_command_integration.py::TestFieldValueCommandIntegration::test_multiple_commands_sequence", "tests/test_field_value_command_integration.py::TestFieldValueRepositoryCommandIntegration::test_end_to_end_undo_flow", "tests/test_field_value_command_integration.py::TestFieldValueRepositoryCommandIntegration::test_repository_create_called_with_correct_parameters", "tests/test_field_value_command_integration.py::TestFieldValueRepositoryCommandIntegration::test_repository_stores_original_context_not_edited", "tests/test_field_value_command_integration.py::TestFieldValueRepositoryCommandIntegration::test_store_command_saves_context_without_modification", "tests/test_field_value_command_integration.py::TestFieldValueRepositoryCommandIntegration::test_summary_regeneration_on_field_update", "tests/test_field_value_command_integration.py::TestFieldValueRepositoryCommandIntegration::test_undo_command_reverts_field_value", "tests/test_get_last_message_refactored.py::TestGetLastMessageRefactored::test_get_last_message_aggregated_data_fully_filled", "tests/test_get_last_message_refactored.py::TestGetLastMessageRefactored::test_get_last_message_aggregated_data_partially_filled", "tests/test_get_last_message_refactored.py::TestGetLastMessageRefactored::test_get_last_message_confirmed_data_fully_filled", "tests/test_get_last_message_refactored.py::TestGetLastMessageRefactored::test_get_last_message_confirmed_data_partially_filled", "tests/test_get_last_message_refactored.py::TestGetLastMessageRefactored::test_get_last_message_confirmed_data_update_logic", "tests/test_get_last_message_refactored.py::TestGetLastMessageRefactored::test_get_last_message_more_than_two_dates_field_prevents_auto_confirmation", "tests/test_get_last_message_refactored.py::TestGetLastMessageRefactored::test_get_last_message_system_generation_error", "tests/test_get_last_message_refactored.py::TestGetLastMessageRefactored::test_get_last_message_system_message_exists", "tests/test_get_last_message_refactored.py::TestGetLastMessageRefactored::test_get_last_message_user_message_no_extracted_data", "tests/test_get_last_message_refactored.py::TestGetLastMessageRefactored::test_get_last_message_user_message_text_type", "tests/test_get_last_message_refactored.py::TestGetLastMessageRefactored::test_get_last_message_user_message_with_extracted_data", "tests/test_health_endpoint.py::test_health_check", "tests/test_kx_dash_endpoints.py::test_get_activity_invalid_id", "tests/test_kx_dash_endpoints.py::test_get_activity_not_found", "tests/test_kx_dash_endpoints.py::test_get_activity_success", "tests/test_kx_dash_endpoints.py::test_kx_dash_service_sorting_and_filtering_logic", "tests/test_kx_dash_endpoints.py::test_list_activities_empty_result", "tests/test_kx_dash_endpoints.py::test_list_activities_success", "tests/test_kx_dash_endpoints.py::test_list_activities_with_sorting_and_filtering", "tests/test_message_endpoints.py::test_client_names_denial", "tests/test_message_endpoints.py::test_create_message_ai_bad_request_error", "tests/test_message_endpoints.py::test_create_message_client_name_confirmation_data_complete", "tests/test_message_endpoints.py::test_create_message_dash_task", "tests/test_message_endpoints.py::test_create_message_fail_on_file_size", "tests/test_message_endpoints.py::test_create_message_generate_qual_after_dash_discard", "tests/test_message_endpoints.py::test_create_message_ldmf_country_confirmation_data_complete", "tests/test_message_endpoints.py::test_create_message_multiple_client_names_confirmation_data_complete[parametrized_data0]", "tests/test_message_endpoints.py::test_create_message_multiple_client_names_confirmation_data_complete[parametrized_data1]", "tests/test_message_endpoints.py::test_create_message_nonexistent_conversation", "tests/test_message_endpoints.py::test_create_message_success[parametrized_data0]", "tests/test_message_endpoints.py::test_create_message_success[parametrized_data1]", "tests/test_message_endpoints.py::test_create_message_success[parametrized_data2]", "tests/test_message_endpoints.py::test_create_message_unable_to_get_internal_id_", "tests/test_message_endpoints.py::test_create_message_validation_errors[invalid_data0]", "tests/test_message_endpoints.py::test_create_message_validation_errors[invalid_data1]", "tests/test_message_endpoints.py::test_create_message_with_multiple_client_names_requires_confirmation", "tests/test_message_endpoints.py::test_create_message_with_multiple_files_no_content", "tests/test_message_endpoints.py::test_create_message_with_one_date", "tests/test_message_endpoints.py::test_create_message_with_only_files", "tests/test_message_endpoints.py::test_dash_tasks_denial", "tests/test_message_endpoints.py::test_get_message_invalid_uuid", "tests/test_message_endpoints.py::test_get_message_not_found", "tests/test_message_endpoints.py::test_get_message_success", "tests/test_message_endpoints.py::test_get_message_wrong_user_id", "tests/test_message_endpoints.py::test_ldmf_countries_denial", "tests/test_message_endpoints.py::test_manual_ldmf_input_invalid_country", "tests/test_message_endpoints.py::test_manual_ldmf_input_multiple_matches", "tests/test_message_endpoints.py::test_manual_ldmf_input_valid_country", "tests/test_openapy.py::test_openapi", "tests/test_outcomes_handler_integration.py::TestEndToEndMessageFlow::test_confirmed_data_storage_and_retrieval", "tests/test_outcomes_handler_integration.py::TestEndToEndMessageFlow::test_conversation_state_tracking", "tests/test_outcomes_handler_integration.py::TestEndToEndMessageFlow::test_uncertain_intent_triggers_missing_data_detection", "tests/test_outcomes_handler_integration.py::TestErrorHandlingAndEdgeCases::test_malformed_confirmed_data_json", "tests/test_outcomes_handler_integration.py::TestErrorHandlingAndEdgeCases::test_nonexistent_conversation_id", "tests/test_outcomes_handler_integration.py::TestExtractedDataServiceIntegration::test_missing_data_detection_outcomes_confirmed", "tests/test_outcomes_handler_integration.py::TestExtractedDataServiceIntegration::test_missing_data_detection_outcomes_in_aggregated_data", "tests/test_outcomes_handler_integration.py::TestExtractedDataServiceIntegration::test_missing_data_detection_outcomes_missing", "tests/test_outcomes_handler_integration.py::TestExtractedDataServiceIntegration::test_progressive_data_collection_flow", "tests/test_outcomes_handler_integration.py::TestOutcomesHandlerUnit::test_confirmed_data_overrides_aggregated_data", "tests/test_outcomes_handler_integration.py::TestOutcomesHandlerUnit::test_outcomes_already_confirmed", "tests/test_outcomes_handler_integration.py::TestOutcomesHandlerUnit::test_outcomes_in_aggregated_data_not_confirmed", "tests/test_outcomes_handler_integration.py::TestOutcomesHandlerUnit::test_outcomes_missing_entirely", "tests/test_outcomes_handler_integration.py::TestThreeScenarioHandling::test_multiple_values_scenario_priority_override", "tests/test_outcomes_handler_integration.py::TestThreeScenarioHandling::test_one_value_scenario", "tests/test_outcomes_handler_integration.py::TestThreeScenarioHandling::test_zero_values_scenario", "tests/test_proactive_chat.py::TestProactiveChatService::test_all_fields_completed", "tests/test_proactive_chat.py::TestProactiveChatService::test_all_fields_missing", "tests/test_proactive_chat.py::TestProactiveChatService::test_completion_progress", "tests/test_proactive_chat.py::TestProactiveChatService::test_completion_progress_all_completed", "tests/test_proactive_chat.py::TestProactiveChatService::test_enriched_message_missing_fields", "tests/test_proactive_chat.py::TestProactiveChatService::test_enriched_message_multiple_ldmf_countries", "tests/test_proactive_chat.py::TestProactiveChatService::test_enriched_message_partial_fields", "tests/test_proactive_chat.py::TestProactiveChatService::test_next_required_field", "tests/test_proactive_chat.py::TestProactiveChatService::test_next_required_field_all_completed", "tests/test_proactive_chat.py::TestProactiveChatService::test_pending_confirmation_multiple_ldmf_countries", "tests/test_prompt_and_documents_parser.py::TestPromptAndDocumentDataParser::test_call_with_all_fields", "tests/test_prompt_and_documents_parser.py::TestPromptAndDocumentDataParser::test_call_with_no_fields", "tests/test_prompt_and_documents_parser.py::TestPromptAndDocumentDataParser::test_call_with_partial_fields", "tests/test_prompt_and_documents_parser.py::TestPromptAndDocumentDataParser::test_get_date_for_extracted_data_invalid_string", "tests/test_prompt_and_documents_parser.py::TestPromptAndDocumentDataParser::test_get_date_for_extracted_data_unexpected_type", "tests/test_prompt_and_documents_parser.py::TestPromptAndDocumentDataParser::test_get_date_for_extracted_data_valid_inputs[2023-03-01-expected_date2]", "tests/test_prompt_and_documents_parser.py::TestPromptAndDocumentDataParser::test_get_date_for_extracted_data_valid_inputs[None-None]", "tests/test_prompt_and_documents_parser.py::TestPromptAndDocumentDataParser::test_get_date_for_extracted_data_valid_inputs[input_val1-expected_date1]", "tests/test_qual_endpoints.py::TestConvertQualIdToConversationId::test_convert_qual_id_to_conversation_id_case_sensitivity", "tests/test_qual_endpoints.py::TestConvertQualIdToConversationId::test_convert_qual_id_to_conversation_id_invalid_formats[   ]", "tests/test_qual_endpoints.py::TestConvertQualIdToConversationId::test_convert_qual_id_to_conversation_id_invalid_formats[123]", "tests/test_qual_endpoints.py::TestConvertQualIdToConversationId::test_convert_qual_id_to_conversation_id_invalid_formats[QUAL-WITH-SPECIAL-CHARS-@#$%]", "tests/test_qual_endpoints.py::TestConvertQualIdToConversationId::test_convert_qual_id_to_conversation_id_invalid_formats[VERY-LONG-QUAL-ID-XXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXX]", "tests/test_qual_endpoints.py::TestConvertQualIdToConversationId::test_convert_qual_id_to_conversation_id_invalid_formats[]", "tests/test_qual_endpoints.py::TestConvertQualIdToConversationId::test_convert_qual_id_to_conversation_id_invalid_formats[qual-with-unicode-\\xf1\\xe1\\xe9\\xed\\xf3\\xfa]", "tests/test_qual_endpoints.py::TestConvertQualIdToConversationId::test_convert_qual_id_to_conversation_id_not_found", "tests/test_qual_endpoints.py::TestConvertQualIdToConversationId::test_convert_qual_id_to_conversation_id_permission_integration", "tests/test_qual_endpoints.py::TestConvertQualIdToConversationId::test_convert_qual_id_to_conversation_id_success", "tests/test_qual_endpoints.py::TestConvertQualIdToConversationId::test_convert_qual_id_to_conversation_id_wrong_user", "tests/test_qual_endpoints.py::TestGetCombinedExtractedDataByQualId::test_get_combined_extracted_data_by_qual_id_not_found", "tests/test_qual_endpoints.py::TestGetCombinedExtractedDataByQualId::test_get_combined_extracted_data_by_qual_id_success", "tests/test_qual_endpoints.py::TestGetCombinedExtractedDataByQualId::test_get_combined_extracted_data_by_qual_id_wrong_user", "tests/test_quals_clients.py::TestQualsClientsRepository::test_create_client_mock", "tests/test_quals_clients.py::TestQualsClientsRepository::test_create_client_real_error", "tests/test_quals_clients.py::TestQualsClientsRepository::test_create_client_real_success", "tests/test_quals_clients.py::TestQualsClientsRepository::test_create_client_with_special_characters", "tests/test_quals_clients.py::TestQualsClientsRepository::test_search_clients_empty_results", "tests/test_quals_clients.py::TestQualsClientsRepository::test_search_clients_mock", "tests/test_quals_clients.py::TestQualsClientsRepository::test_search_clients_pagination", "tests/test_quals_clients.py::TestQualsClientsRepository::test_search_clients_real_error", "tests/test_quals_clients.py::TestQualsClientsRepository::test_search_clients_real_list_response", "tests/test_suggested_prompts_generator.py::TestSuggestedPromptsGenerator::test_dash_discard_message_intention", "tests/test_suggested_prompts_generator.py::test_enter_a_new_client_suggestion_for_multiple_client_names", "tests/test_suggested_prompts_generator.py::test_no_create_my_qual_not_suggested_when_fields_incomplete", "tests/test_suggested_prompts_generator.py::test_no_create_my_qual_suggestion_when_all_fields_complete", "tests/test_suggested_prompts_generator.py::test_yes_this_is_correct_suggestion_for_single_client_name", "tests/test_suggestions_qual_field.py::TestMessageContentSuggestionsQual::test_message_content_to_message_validator", "tests/test_suggestions_qual_field.py::TestMessageContentSuggestionsQual::test_message_content_with_suggestions_qual", "tests/test_suggestions_qual_field.py::TestMessageValidatorSuggestionsQual::test_create_with_empty_suggestions_qual", "tests/test_suggestions_qual_field.py::TestMessageValidatorSuggestionsQual::test_create_with_suggestions_qual_list", "tests/test_suggestions_qual_field.py::TestMessageValidatorSuggestionsQual::test_model_dump_for_db_empty_suggestions_qual", "tests/test_suggestions_qual_field.py::TestMessageValidatorSuggestionsQual::test_model_dump_for_db_with_suggestions_qual", "tests/test_suggestions_qual_field.py::TestMessageValidatorSuggestionsQual::test_validate_suggestions_qual_empty_values", "tests/test_suggestions_qual_field.py::TestMessageValidatorSuggestionsQual::test_validate_suggestions_qual_from_dict_list", "tests/test_suggestions_qual_field.py::TestMessageValidatorSuggestionsQual::test_validate_suggestions_qual_from_json_string", "tests/test_suggestions_qual_field.py::TestMessageValidatorSuggestionsQual::test_validate_suggestions_qual_invalid_item_type", "tests/test_suggestions_qual_field.py::TestMessageValidatorSuggestionsQual::test_validate_suggestions_qual_invalid_json", "tests/test_suggestions_qual_field.py::TestMessageValidatorSuggestionsQual::test_validate_suggestions_qual_invalid_type", "tests/test_suggestions_qual_field.py::TestStructuredSuggestedPrompt::test_create_empty", "tests/test_suggestions_qual_field.py::TestStructuredSuggestedPrompt::test_create_with_all_fields", "tests/test_suggestions_qual_field.py::TestStructuredSuggestedPrompt::test_create_with_partial_fields", "tests/test_suggestions_qual_field.py::TestStructuredSuggestedPrompt::test_model_dump", "tests/test_suggestions_qual_field.py::TestSystemMessageSerializerSuggestionsQual::test_validate_suggestions_qual_from_db", "tests/test_system_message_generation.py::TestSystemMessageGenerationService::test_generate_for_ldmf_country_confirmed_priority", "tests/test_system_message_generation.py::TestSystemMessageGenerationService::test_generate_for_ldmf_country_multiple_options", "tests/test_system_message_generation.py::TestSystemMessageGenerationService::test_generate_for_ldmf_country_no_aggregated", "tests/test_system_message_generation.py::TestSystemMessageGenerationService::test_generate_for_ldmf_country_single_option", "tests/test_system_message_generation.py::TestSystemMessageGenerationService::test_generate_options_client_names", "tests/test_system_message_generation.py::TestSystemMessageGenerationService::test_generate_options_date_intervals", "tests/test_system_message_generation.py::TestSystemMessageGenerationService::test_generate_options_empty_data", "tests/test_system_message_generation.py::TestSystemMessageGenerationService::test_generate_options_ldmf_countries", "tests/test_system_message_generation.py::TestSystemMessageGenerationService::test_generate_options_priority_order_client_names", "tests/test_system_message_generation.py::TestSystemMessageGenerationService::test_generate_options_priority_order_dates", "tests/test_system_message_generation.py::TestSystemMessageGenerationService::test_generate_options_priority_order_ldmf_countries", "tests/test_system_message_generation.py::TestSystemMessageGenerationService::test_generate_system_message_confirmed_data_complete", "tests/test_system_message_generation.py::TestSystemMessageGenerationService::test_generate_system_message_confirmed_data_incomplete", "tests/test_system_message_generation.py::TestSystemMessageGenerationService::test_generate_system_message_empty_data", "tests/test_system_message_generation.py::TestSystemMessageGenerationService::test_generate_system_message_incomplete_data", "tests/test_system_message_generation.py::TestSystemMessageGenerationService::test_is_data_complete_false_missing_outcomes", "tests/test_system_message_generation.py::TestSystemMessageGenerationService::test_is_data_complete_false_multiple_clients", "tests/test_system_message_generation.py::TestSystemMessageGenerationService::test_is_data_complete_true", "tests/test_system_message_generation.py::TestSystemMessageGenerationService::test_parse_date_string_invalid", "tests/test_system_message_generation.py::TestSystemMessageGenerationService::test_parse_date_string_none", "tests/test_system_message_generation.py::TestSystemMessageGenerationService::test_parse_date_string_valid", "tests/test_system_message_prompts.py::TestSystemMessagePromptsGenerator::test_field_status_detection", "tests/test_system_message_prompts.py::TestSystemMessagePromptsGenerator::test_generate_system_reply_type_for_all_fields_complete", "tests/test_system_message_prompts.py::TestSystemMessagePromptsGenerator::test_generate_system_reply_type_for_client_confirmation", "tests/test_system_message_prompts.py::TestSystemMessagePromptsGenerator::test_generate_system_reply_type_for_empty_data", "tests/test_system_message_prompts.py::TestSystemMessagePromptsGenerator::test_generate_system_reply_type_for_engagement_dates_missing", "tests/test_system_message_prompts.py::TestSystemMessagePromptsGenerator::test_generate_system_reply_type_for_ldmf_country_missing", "tests/test_system_message_prompts.py::TestSystemMessagePromptsGenerator::test_generate_system_reply_type_for_multiple_client_options", "tests/test_system_message_prompts.py::TestSystemMessagePromptsGenerator::test_generate_system_reply_type_for_multiple_ldmf_countries", "tests/test_system_message_prompts.py::TestSystemMessagePromptsGenerator::test_next_required_field_detection", "tests/test_utils.py::test_truncate_filename_edge_case_exact_length", "tests/test_utils.py::test_truncate_filename_edge_case_just_over_length", "tests/test_utils.py::test_truncate_filename_empty_string", "tests/test_utils.py::test_truncate_filename_max_length_too_small", "tests/test_utils.py::test_truncate_filename_no_truncation", "tests/test_utils.py::test_truncate_filename_special_characters", "tests/test_utils.py::test_truncate_filename_with_truncation", "tests/test_welcome_message.py::TestWelcomeMessage::test_create_welcome_message_with_multiple_dash_tasks", "tests/test_welcome_message.py::TestWelcomeMessage::test_create_welcome_message_with_one_dash_task", "tests/test_welcome_message.py::TestWelcomeMessage::test_create_welcome_message_with_one_dash_task_selected", "tests/test_welcome_message.py::TestWelcomeMessage::test_create_welcome_message_without_dash_tasks"]