import logging
from uuid import UUID

from sqlalchemy import select, update
from sqlalchemy.ext.asyncio import AsyncSession

from constants.extracted_data import ConversationState
from durable_functions.utils.exceptions.entity import EntityNotFoundError
from models import QualConversation
from schemas.confirmed_data import ConfirmedData


__all__ = ['ConversationRepository']


logger = logging.getLogger(__name__)


class ConversationRepository:
    """Repository for conversation-related database operations."""

    def __init__(self, db_session: AsyncSession):
        self.db_session = db_session

    async def get_internal_id(self, public_id: UUID) -> int | None:
        """
        Get a conversation internal ID by its public ID.

        Args:
            public_id: The public ID of the conversation

        Returns:
            The conversation internal ID if found, None otherwise
        """
        return (
            await self.db_session.execute(select(QualConversation.Id).where(QualConversation.PublicId == public_id))
        ).scalar_one_or_none()

    async def get_state(self, public_id: UUID) -> str | None:
        """
        Get a conversation state by its public ID.

        Args:
            public_id: The public ID of the conversation

        Returns:
            The conversation state if found, None otherwise
        """
        return (
            await self.db_session.execute(select(QualConversation.State).where(QualConversation.PublicId == public_id))
        ).scalar_one_or_none()

    async def exists(self, public_id: UUID) -> bool:
        """
        Check if a conversation with specified public ID exists.

        Args:
            public_id: The public ID of the conversation

        Returns:
            True if the conversation is found, False otherwise
        """
        query = select(1).where(QualConversation.PublicId == public_id)
        result = await self.db_session.execute(query)
        return result.scalar() is not None

    async def update_confirmed_data_and_state(
        self, public_id: UUID, confirmed_data: ConfirmedData, state: ConversationState
    ) -> None:
        """
        Update both confirmed data and state for a conversation in a single operation.

        Args:
            public_id: The public ID of the conversation
            confirmed_data: The confirmed data to store
            state: The new conversation state

        Returns:
            None
        """
        logger.debug('Updating confirmed data and state for conversation ID: %s to %s', public_id, state)
        if not await self.exists(public_id):
            raise EntityNotFoundError('Conversation', str(public_id))

        query = (
            update(QualConversation)
            .where(QualConversation.PublicId == public_id)
            .values(ConfirmedData=confirmed_data.to_json_string(), State=state)
        )
        await self.db_session.execute(query)

    async def get_confirmed_data(self, public_id: UUID) -> ConfirmedData:
        """
        Get the confirmed data for a conversation.

        Args:
            public_id: The public ID of the conversation

        Returns:
            ConfirmedData object (empty if no data exists)
        """
        query = select(QualConversation.ConfirmedData).where(QualConversation.PublicId == public_id)
        result = await self.db_session.execute(query)
        confirmed_data_json = result.scalar_one_or_none()
        return ConfirmedData.from_json_string(confirmed_data_json)
