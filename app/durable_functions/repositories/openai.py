import logging
from typing import Type, TypeVar

from openai import AsyncAzureOpenAI
from openai.types.chat import (
    ChatCompletionMessageParam,
    ChatCompletionSystemMessageParam,
    ChatCompletionUserMessageParam,
)
from pydantic import BaseModel

from constants.message import (
    DETECT_LANGUAGE_SYSTEM_PROMPT,
    DETECT_LANGUAGE_USER_PROMPT,
    TRANSLATE_TO_ENGLISH_SYSTEM_PROMPT,
    TRANSLATE_TO_ENGLISH_USER_PROMPT,
)
from constants.prompt import prompt_templates
from durable_functions.application.config import settings
from durable_functions.utils.models import (
    LLMConflictDetected,
    LLMExtractedDataResult,
    LLMExtractedRequiredDataResult,
    LLMFieldConflictsDetected,
)


__all__ = ['OpenAIRepository']


logger = logging.getLogger(__name__)

openai_client = AsyncAzureOpenAI(
    api_key=settings.openai.key,
    api_version=settings.openai.api_version,
    azure_endpoint=settings.openai.endpoint,
)

T = TypeVar('T', bound=BaseModel)


class OpenAIRepository:
    """Repository for interacting with Azure OpenAI API."""

    def __init__(self, client: AsyncAzureOpenAI = openai_client) -> None:
        """Initialize the OpenAI service with settings from the config."""
        self.client = client

    async def _generate_typed_completion(
        self,
        system_prompt: str,
        user_prompt: str,
        response_model: Type[T],
        temperature: float = 0.0,
        model: str = settings.openai.model,
        max_tokens: int | None = None,
    ) -> T | None:
        """
        Generate a typed completion using structured output parsing.

        Args:
            system_prompt: The system message content.
            user_prompt: The user message content.
            response_model: Pydantic model class for structured response.
            temperature: Temperature parameter for controlling randomness.
            model: The specific model to use for the completion.

        Returns:
            Parsed response as the specified Pydantic model, or None if failed.
        """
        try:
            messages: list[ChatCompletionMessageParam] = [
                ChatCompletionSystemMessageParam(role='system', content=system_prompt),
                ChatCompletionUserMessageParam(role='user', content=user_prompt),
            ]
            logger.info(f'Calling typed completion with model: "{model}"')
            logger.debug(f'Input messages to OpenAI: {messages}')

            completion = await self.client.beta.chat.completions.parse(
                model=model,
                messages=messages,
                response_format=response_model,
                temperature=temperature,
                max_tokens=max_tokens,
            )

            message = completion.choices[0].message
            if message.refusal:
                logger.warning('OpenAI refused to generate completion: %s', message.refusal)
                return None

            if message.parsed is None:
                logger.warning('OpenAI returned no parsed data for prompt: %s', user_prompt)
                return None

            logger.info(f'Generated typed completion: {message.parsed}')
            return message.parsed
        except Exception:
            logger.exception('Error generating typed completion')
            return None

    async def _generate_string_completion(
        self,
        system_prompt: str,
        user_prompt: str,
        temperature: float = 0.0,
        model: str = settings.openai.model,
    ) -> str | None:
        """
        Generate a plain string completion.

        Args:
            system_prompt: The system message content.
            user_prompt: The user message content.
            temperature: Temperature parameter for controlling randomness.
            model: The specific model to use for the completion.

        Returns:
            Plain text response from the model, or None if failed.
        """
        try:
            messages: list[ChatCompletionMessageParam] = [
                ChatCompletionSystemMessageParam(role='system', content=system_prompt),
                ChatCompletionUserMessageParam(role='user', content=user_prompt),
            ]
            logger.info(f'Calling string completion with model: "{model}"')
            logger.debug(f'Input messages to OpenAI: {messages}')

            completion = await self.client.chat.completions.create(
                model=model,
                messages=messages,
                temperature=temperature,
            )

            message = completion.choices[0].message
            if not message.content:
                logger.warning('OpenAI returned no content for prompt: %s', user_prompt)
                return None

            logger.info(f'Generated string completion: {message.content}')
            return message.content.strip()
        except Exception:
            logger.exception('Error generating string completion')
            return None

    async def extract_structured_data(
        self,
        system_prompt: str,
        user_prompt: str,
        response_model: Type[T],
        temperature: float = 0.0,
        model: str = settings.openai.model,
        max_tokens: int | None = None,
    ) -> T | None:
        """
        Extract structured data using the specified Pydantic model.

        Args:
            system_prompt: The system message content.
            user_prompt: The user message content.
            response_model: Pydantic model class for structured response.
            temperature: Temperature parameter for controlling randomness.
            model: The specific model to use for the completion.

        Returns:
            Parsed response as the specified Pydantic model, or None if failed.
        """
        return await self._generate_typed_completion(
            system_prompt=system_prompt,
            user_prompt=user_prompt,
            response_model=response_model,
            temperature=temperature,
            model=model,
            max_tokens=max_tokens,
        )

    async def extract_data(
        self,
        text: str,
        temperature: float = 0.0,
        model: str = settings.openai.model,
    ) -> LLMExtractedDataResult:
        """
        Extract data from text using the LLMExtractedDataResult model.

        Args:
            text: The text to extract data from.
            temperature: Temperature parameter for controlling randomness.
            model: The specific model to use for the completion.

        Returns:
            Extracted data as LLMExtractedDataResult, or empty result if failed.
        """
        logger.info(f'Calling extract_data with text: "{text}", temperature: {temperature}, model: "{model}"')

        result = await self._generate_typed_completion(
            system_prompt=prompt_templates.extract_data.SYSTEM,
            user_prompt=prompt_templates.extract_data.USER.format(text=text),
            response_model=LLMExtractedRequiredDataResult,
            temperature=temperature,
            model=model,
        )

        # Return empty result if extraction failed, maintaining backward compatibility
        return result.to_full_model() if result is not None else LLMExtractedDataResult()

    async def extract_ldmf_countries(
        self,
        text: str,
        ldmf_countries_list: list[str],
        temperature: float = 0.0,
        model: str = settings.openai.model,
    ) -> str | None:
        """
        Extract LDMF countries from text using the provided countries list.

        Args:
            text: The text to extract countries from.
            ldmf_countries_list: List of valid LDMF countries.
            temperature: Temperature parameter for controlling randomness.
            model: The specific model to use for the completion.

        Returns:
            Extracted country name, or None if extraction failed.
        """
        source_list = '\n'.join(ldmf_countries_list)
        system_prompt = prompt_templates.extract_ldmf_country.SYSTEM.format(source_list=source_list)
        user_prompt = prompt_templates.extract_ldmf_country.USER.format(user_message=text)

        result = await self._generate_string_completion(
            system_prompt=system_prompt,
            user_prompt=user_prompt,
            temperature=temperature,
            model=model,
        )

        if result is None:
            logger.warning(f'Failed to extract LDMF countries from text: {text}')
            return None

        # Clean up quotes from the result
        cleaned_result = result.replace('"', '')
        logger.info(f'Extracted LDMF countries: {cleaned_result}')
        return cleaned_result

    async def detect_language(
        self,
        text: str,
        temperature: float = 0.0,
        model: str = settings.openai.model,
    ) -> str | None:
        """
        Detect the language of the given text using an LLM.
        Args:
            text: The text to detect language from.
            temperature: Temperature parameter for controlling randomness.
            model: The specific model to use for the completion.
        Returns:
            Detected language as a string, or None if detection failed.
        """
        result = await self._generate_string_completion(
            system_prompt=DETECT_LANGUAGE_SYSTEM_PROMPT,
            user_prompt=DETECT_LANGUAGE_USER_PROMPT.format(text=text),
            temperature=temperature,
            model=model,
        )
        if result is None:
            logger.warning(f'Failed to detect language from text: {text}')
            return None
        return result

    async def translate_text(
        self,
        text: str,
        source_language: str,
        temperature: float = 0.0,
        model: str = settings.openai.model,
    ) -> str | None:
        """Translate text from a given language to English using an LLM.
        Args:
            text: The original content to translate.
            source_language: The language of the original content.
            temperature: Temperature parameter for controlling randomness.
            model: The specific model to use for the completion.
        Returns:
            Translated text in English, or None if translation failed.
        """
        result = await self._generate_string_completion(
            system_prompt=TRANSLATE_TO_ENGLISH_SYSTEM_PROMPT,
            user_prompt=TRANSLATE_TO_ENGLISH_USER_PROMPT.format(
                language=source_language,
                text=text,
            ),
            temperature=temperature,
            model=model,
        )
        if result is None:
            logger.warning(f'Failed to translate text: {text}')
            return None
        return result

    async def extract_conflicts_details(
        self,
        text: str,
        field: str,
        model: str = settings.openai.model,
    ) -> list[LLMConflictDetected]:
        """
        Detect if given text for objective_scopes contains contradictions

        Args:
            text: The text to extract data from.
            temperature: Temperature parameter for controlling randomness.
            model: The specific model to use for the completion.

        Returns:
            Details of conflicts as LLMConflictDetected, or empty list if text have no conflicts.
        """

        FIELD_PROMPT_MAPPING = {
            'objective_and_scope': prompt_templates.extract_objective_scopes_conflicts,
            'outcomes': prompt_templates.extract_outcomes_conflicts,
        }
        logger.info(
            f'Calling extract_conflicts_details with text: "{FIELD_PROMPT_MAPPING[field].USER.format(text=text)}", model: "{model}"'
        )

        llm_result = await self._generate_typed_completion(
            system_prompt=FIELD_PROMPT_MAPPING[field].SYSTEM,
            user_prompt=FIELD_PROMPT_MAPPING[field].USER.format(text=text),
            response_model=LLMFieldConflictsDetected,
            model=model,
        )
        if not llm_result:
            return []
        payloads = []
        for conflict in llm_result.conflicts:
            payload = conflict.model_dump(exclude_unset=True, exclude_none=True)
            payload['field'] = field
            payloads.append(payload)

        return [LLMConflictDetected(**payload) for payload in payloads]
