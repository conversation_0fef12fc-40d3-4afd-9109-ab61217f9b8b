import logging
from uuid import UUID

from sqlalchemy import func, select
from sqlalchemy.ext.asyncio import AsyncSession

from durable_functions.utils.exceptions.entity import EntityNotFoundError
from durable_functions.utils.models import LLMConflictDetected
from models import QualConflict
from schemas import Conflict

from .conversation import ConversationRepository


__all__ = ['ConflictRepository']


logger = logging.getLogger(__name__)


class ConflictRepository:
    """Repository for extracted data-related database operations."""

    def __init__(self, db_session: AsyncSession, conversation_repository: ConversationRepository):
        self.db_session = db_session
        self.conversation_repository = conversation_repository

    async def create_many(
        self,
        conversation_id: UUID,
        conflicts: list[LLMConflictDetected],
    ) -> list[Conflict] | None:
        """
        Create multiple QualConflict records for the given conversation.

        Args:
            conversation_id: Public UUID of the conversation
            conflicts: LLMConflictDetected objects containing all conflicts
            field: Conflict field name

        Returns:
            List of ConflictResolve objects representing created conflicts
        """
        # Get internal DB ID for the conversation
        conversation_internal_id = await self.conversation_repository.get_internal_id(conversation_id)
        if conversation_internal_id is None:
            raise EntityNotFoundError('Conversation', str(conversation_id))

        new_conflicts = []
        for counter, conflict in enumerate(conflicts, start=1):
            new_conflicts.append(
                QualConflict(
                    ConversationId=conversation_internal_id,
                    Description=f'Conflict {counter}: {conflict.description}',
                    ConflictingValues=conflict.conflicting_values,
                    Field=conflict.field,
                    ChosenValue=None,
                )
            )

        self.db_session.add_all(new_conflicts)
        await self.db_session.flush()
        return [Conflict.model_validate(conflict) for conflict in new_conflicts] if new_conflicts else None

    async def check_conversation_conflicts(self, conversation_id: UUID) -> bool | None:
        """
        Check if conflicts are resolved for a conversation.

        Returns:
            None  -> no conflicts at all
            False -> some conflicts exist with ChosenValue IS NULL
            True  -> conflicts exist and all have ChosenValue NOT NULL
        """
        conversation_internal_id = await self.conversation_repository.get_internal_id(conversation_id)
        if conversation_internal_id is None:
            raise EntityNotFoundError('Conversation', str(conversation_id))

        query = (
            select(func.count())
            .select_from(QualConflict)
            .where(QualConflict.ConversationId == conversation_internal_id)
        )
        total_conflicts = (await self.db_session.execute(query)).scalar_one()

        if total_conflicts == 0:
            return None

        query_unresolved = (
            select(func.count())
            .select_from(QualConflict)
            .where(QualConflict.ConversationId == conversation_internal_id, QualConflict.ChosenValue.is_(None))
        )
        unresolved_conflicts = (await self.db_session.execute(query_unresolved)).scalar_one()

        return unresolved_conflicts == 0
