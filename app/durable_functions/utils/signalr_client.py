import datetime
import logging
from typing import Any, Dict, Optional
from urllib.parse import urljoin

import aiohttp
import jwt

from durable_functions.application.config import settings

from .models import JwtClaimNames, UserConnectionAttributes


logger = logging.getLogger(__name__)


class SignalRApiClient:
    """Client for sending SignalR notifications using the REST API."""

    def __init__(self, connection_string: Optional[str] = None):
        """
        Initialize the SignalR REST API client.

        Args:
            connection_string: Azure SignalR connection string
        """
        connection_str = connection_string or settings.SIGNALR_SETTINGS.CONNECTION_STRING
        self.endpoint, self.access_key = self._parse_connection_string(connection_str)

        if not self.endpoint or not self.access_key:
            raise ValueError('SignalR Endpoint or Access Key could not be parsed from connection string')

        self.endpoint = self.endpoint.rstrip('/')
        self.hub_name = settings.SIGNALR_SETTINGS.HUB_NAME

    def _parse_connection_string(self, connection_string: str) -> tuple[str, str]:
        """Parse the Azure SignalR connection string to extract endpoint and access key."""
        if not connection_string:
            raise ValueError('SignalR connection string is not configured')

        parts = connection_string.split(';')
        cs_dict = {part.split('=', 1)[0].lower(): part.split('=', 1)[1] for part in parts if '=' in part}

        return cs_dict['endpoint'], cs_dict['accesskey']

    def _generate_jwt(self, url: str, subject: str | None = None) -> str:
        """
        Generate a JWT token for Azure SignalR REST API authentication.

        Args:
            audience: The audience URL for the token

        Returns:
            JWT token string
        """
        claims = {
            JwtClaimNames.EXP.value: datetime.datetime.now(datetime.timezone.utc) + datetime.timedelta(minutes=60),
            JwtClaimNames.AUD.value: url,
        }
        if subject:
            claims[JwtClaimNames.SUB.value] = subject

        return jwt.encode(claims, self.access_key, algorithm='HS256')

    async def send_notification(self, event_type: str, data: Dict[str, Any], user_id: str | None = None) -> None:
        """
        Send a notification via SignalR REST API.

        Args:
            event_type: Type of event (method name to invoke on clients)
            data: Event data
            user_id: Optional user ID to target specific user

        Returns:
            None
        """
        url = f'{self.endpoint}/api/v1/hubs/{self.hub_name}'

        # If user_id is provided, target that specific user
        if user_id:
            url = f'{url}/users/{user_id}'

        audience = url
        token = self._generate_jwt(audience)

        headers = {
            'Authorization': f'Bearer {token}',
            'Content-Type': 'application/json',
        }

        payload = {'target': event_type, 'arguments': [data]}

        try:
            async with aiohttp.ClientSession() as session:
                logger.info(f'Sending SignalR notification to {url}')
                logger.debug(f'Payload: {payload}')

                async with session.post(url, json=payload, headers=headers) as response:
                    if response.status != 202:
                        error_text = await response.text()
                        logger.error(f'SignalR REST API error: {response.status} - {error_text}')
                        response.raise_for_status()
                    else:
                        logger.info(f'Successfully sent SignalR notification: {event_type}')

        except aiohttp.ClientError:
            logger.exception('SignalR REST API client error')
            raise
        except Exception:
            logger.exception('Error sending SignalR notification')
            raise

    def get_user_connection_attributes(self, user_id: str) -> UserConnectionAttributes:
        url = self._get_client_connection_url()
        access_token = self._generate_jwt(url, user_id)
        return UserConnectionAttributes(url=url, access_token=access_token)

    def _get_client_connection_url(self) -> str:
        return urljoin(self.endpoint, f'client/?hub={self.hub_name}')
