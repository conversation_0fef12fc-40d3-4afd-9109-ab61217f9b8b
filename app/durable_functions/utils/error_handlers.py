"""
Utility functions for handling errors consistently across orchestrators.
"""

import logging
from typing import Any, Generator

import azure.durable_functions as df

from constants.durable_functions import ActivityName, EventType, ProcessingStatus
from constants.extracted_data import ConfirmedDataFields
from constants.message import SystemReplyType
from durable_functions.activities.models import (
    SaveExtractionDataActivityOutput,
    SendNotificationActivityInput,
    UpdateProcessingStatusActivityInput,
)


def run_saved_extracted_data_post_processing(
    *,
    save_extraction_data_output: SaveExtractionDataActivityOutput,
    context: df.DurableOrchestrationContext,
    message_id: str,
    signalr_user_id: str,
    logger: logging.Logger,
) -> Generator[Any, Any, None]:
    """
    Helper function to handle ConfirmedDataReplacementError consistently across orchestrators.
    """

    replaced_confirmed_fields: list[ConfirmedDataFields] | None = save_extraction_data_output.replaced_confirmed_fields
    if not replaced_confirmed_fields:
        return

    reply_type = SystemReplyType.CONFIRMED_FIELDS_CHANGE_PROHIBITED
    error_msg = reply_type.message_text
    error_type = reply_type.value
    affected_fields = [f.value for f in replaced_confirmed_fields]

    # Update processing status
    upd_res = yield context.call_activity(
        ActivityName.UpdateProcessingStatus,
        UpdateProcessingStatusActivityInput(
            message_id=message_id,
            status=ProcessingStatus.ConfirmedFieldsChangeProhibited,
            message=error_msg,
            metadata={'error_type': error_type, 'affected_fields': affected_fields},
        ),
    )
    logger.info(f'upd_res in run_saved_extracted_data_post_processing: {upd_res}')

    # Send notification about prohibited field changes
    notif_res = yield context.call_activity(
        ActivityName.SendNotification,
        SendNotificationActivityInput(
            event_type=EventType.UnifiedProcessingError,
            data={
                'message_id': message_id,
                'error': error_msg,
                'error_reason': error_type,
                'affected_fields': affected_fields,
            },
            signalr_user_id=signalr_user_id,
        ),
    )
    logger.info(f'notif_res in run_saved_extracted_data_post_processing: {notif_res}')
