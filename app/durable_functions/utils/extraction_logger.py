import logging
from typing import Any, Dict, Optional

from durable_functions.activities.models import BaseExtractionResult


LLM_TRACE_PREFIX = '[LLM_TRACE]'
BACKEND_PROCESSED_PREFIX = '[BACKEND_PROCESSED]'


def log_extraction_data(
    logger: logging.Logger,
    field_name: str,
    raw_response: Optional[BaseExtractionResult],
    structured_data: Optional[Dict[str, Any]],
):
    """
    Log the raw and structured data for a given field.

    :param logger: The logger instance to use.
    :param field_name: The name of the field being processed.
    :param raw_response: The raw response from the LLM.
    :param structured_data: The processed and structured data.
    """
    logger.info(
        f'{LLM_TRACE_PREFIX} Enhanced extraction for field: {field_name}. #{field_name} '
        f'Raw response: {raw_response}. Structured data: {structured_data}.'
    )


def log_processed_data(logger: logging.Logger, data: Dict[str, Any]):
    """
    Log the backend-processed data.

    :param logger: The logger instance to use.
    :param data: The processed data to log.
    """
    for field, value in data.items():
        logger.info(f'{BACKEND_PROCESSED_PREFIX} Processed field: {field}. #{field} Processed Value: {value}.')
