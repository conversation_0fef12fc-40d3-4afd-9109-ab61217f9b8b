import logging

import azure.durable_functions as df

from constants.durable_functions import (
    ActivityName,
    EventType,
    OrchestratorInputType,
    OrchestratorName,
    ProcessingStatus,
)
from constants.extracted_data import DataSourceType
from durable_functions.activities.models import (
    AggregateMultiSourceDataActivityInput,
    SaveAggregatedResultsToBlobActivityInput,
    SaveConflictsActivityInput,
    SaveExtractionDataActivityInput,
    SaveExtractionDataActivityOutput,
    SendFinalQueueMessageActivityInput,
    SendNotificationActivityInput,
    UpdateProcessingStatusActivityInput,
)
from durable_functions.utils import parse_blob_url
from durable_functions.utils.error_handlers import run_saved_extracted_data_post_processing
from durable_functions.utils.extracted_data_merger import ExtractedDataMerger
from durable_functions.utils.models import FinalExtractionDataResults

from .models import (
    CorruptedDocument,
    CorruptedDocumentsErrorPayload,
    UnifiedProcessingInput,
    UnifiedProcessingOutput,
    UnifiedProcessingOutputFailed,
)


logger = logging.getLogger(__name__)
bp = df.Blueprint()


@bp.orchestration_trigger('context', OrchestratorName.UnifiedProcessing)
def unified_processing_orchestrator(context: df.DurableOrchestrationContext):
    """
    Orchestrator function for unified processing of text prompts and documents.

    This orchestrator coordinates parallel processing of multiple sources:
    1. Process text prompts and documents in parallel using existing orchestrators
    2. Aggregate results from all sources with proper field-specific merging
    3. Save aggregated results to blob storage
    4. Send final queue message for further processing

    Args:
        context: Durable orchestration context

    Returns:
        Dictionary containing the unified processing results
    """

    input_dict = context.get_input()
    logger.info(f'Starting unified processing with input: {input_dict}')

    try:
        input_data = UnifiedProcessingInput.model_validate(input_dict)
        signalr_user_id = input_data.signalr_user_id

        # Extract message_id from the first available source
        message_id = None
        if input_data.text_prompt:
            message_id, _ = parse_blob_url(input_data.text_prompt, is_prompt=True)
        elif input_data.documents:
            message_id, _ = parse_blob_url(input_data.documents[0])

        if not message_id:
            raise ValueError('No valid sources provided for processing')

        logger.info(f'Processing unified message for message_id: {message_id}')

        # Update status to indicate unified processing started
        yield context.call_activity(
            ActivityName.UpdateProcessingStatus,
            UpdateProcessingStatusActivityInput(
                message_id=message_id,
                status=ProcessingStatus.UnifiedProcessingStarted,
                message='Starting unified processing of text and documents',
            ),
        )

        # Send notification
        yield context.call_activity(
            ActivityName.SendNotification,
            SendNotificationActivityInput(
                event_type=EventType.UnifiedProcessingStarted,
                data={
                    'message_id': message_id,
                    'text_prompt': input_data.text_prompt,
                    'documents': input_data.documents,
                },
                signalr_user_id=input_data.signalr_user_id,
            ),
        )

        # Start parallel processing tasks
        parallel_tasks = []
        source_types = []

        # Process text prompt if present
        if input_data.text_prompt:
            prompt_input = {
                'prompt_url': input_data.text_prompt,
                'type': OrchestratorInputType.Prompt,
                'signalr_user_id': input_data.signalr_user_id,
                'is_part_of_unified_processing': True,
            }
            parallel_tasks.append(context.call_sub_orchestrator(OrchestratorName.DocumentProcessing, prompt_input))
            source_types.append(DataSourceType.PROMPT)

        # Process documents if present (max 3 files)
        if input_data.documents:
            for document_url in input_data.documents[:3]:  # Limit to max 3 files
                document_input = {
                    'blob_url': document_url,
                    'type': OrchestratorInputType.Document,
                    'signalr_user_id': input_data.signalr_user_id,
                    'is_part_of_unified_processing': True,
                }
                parallel_tasks.append(
                    context.call_sub_orchestrator(OrchestratorName.DocumentProcessing, document_input)
                )
                source_types.append(DataSourceType.DOCUMENTS)

        # Wait for all parallel processing to complete
        processing_results = yield context.task_all(parallel_tasks)

        logger.info(f'Parallel processing completed. Results: {len(processing_results)}')

        # Pre-aggregate multiple document results before saving to database
        # This fixes the issue where multiple documents overwrite each other due to unique constraint
        document_results = []
        prompt_result = None
        merged_document_result = None  # Initialize to None

        # Track processing statistics for error handling
        total_document_tasks = sum(1 for source_type in source_types if source_type == DataSourceType.DOCUMENTS)
        successful_document_results = 0
        failed_document_results = 0
        corrupted_documents: list[CorruptedDocument] = []
        prompt_succeeded = False
        deferred_corruption_events = []  # Collect corruption events to send later

        for i, results in enumerate(processing_results):
            source_type = source_types[i]

            # Check if the result is successful (has metadata field)
            # Failed results will have 'error' field instead of 'metadata'
            if 'metadata' not in results:
                logger.warning(f'Skipping failed {source_type.value} processing result: {results}')
                if source_type == DataSourceType.DOCUMENTS:
                    failed_document_results += 1
                    corrupted_documents.append(
                        CorruptedDocument(file_name=results.get('file_name', ''), error=results.get('error', ''))
                    )

                # Collect deferred corruption events from failed results
                if 'deferred_corruption_event' in results and results['deferred_corruption_event']:
                    deferred_corruption_events.append(results['deferred_corruption_event'])
                    logger.info(
                        f'Collected deferred corruption event: {results["deferred_corruption_event"]["event_type"]}'
                    )

                continue

            if source_type == DataSourceType.DOCUMENTS:
                # Collect document results for pre-aggregation
                document_results.append(FinalExtractionDataResults.model_validate(results['metadata']))
                successful_document_results += 1
            elif source_type == DataSourceType.PROMPT:
                prompt_result = FinalExtractionDataResults.model_validate(results['metadata'])
                prompt_succeeded = True

        # Send individual corruption notifications first
        if deferred_corruption_events:
            logger.info(f'Sending {len(deferred_corruption_events)} individual deferred corruption events')
            for event in deferred_corruption_events:
                yield context.call_activity(
                    ActivityName.SendNotification,
                    SendNotificationActivityInput(
                        event_type=event['event_type'],
                        data=event['data'],
                        signalr_user_id=event['signalr_user_id'],
                    ),
                )

        # Send DocumentsAreCorruptedError event exactly once if ANY documents are corrupted
        # This prevents RequiredFieldsExtracted from being sent later
        documents_are_corrupted = len(corrupted_documents) > 0
        if documents_are_corrupted:
            logger.info(
                f'Sending DocumentsAreCorruptedError event once for {len(corrupted_documents)} corrupted documents'
            )
            yield context.call_activity(
                ActivityName.SendNotification,
                SendNotificationActivityInput(
                    event_type=EventType.DocumentsAreCorruptedError,
                    data=CorruptedDocumentsErrorPayload(
                        message_id=message_id,
                        docs=corrupted_documents,
                    ).model_dump(),
                    signalr_user_id=input_data.signalr_user_id,
                ),
            )

        # Check if all files in the batch are corrupted (only applies to document processing)
        # If there are documents and ALL of them failed, and no prompt succeeded, send error event
        all_documents_failed = total_document_tasks > 0 and successful_document_results == 0
        no_successful_processing = not prompt_succeeded and successful_document_results == 0

        if all_documents_failed and no_successful_processing:
            logger.error(
                f'All files in batch failed processing - {failed_document_results} documents failed, no prompt processing'
            )

            # Update status to indicate unified processing error
            yield context.call_activity(
                ActivityName.UpdateProcessingStatus,
                UpdateProcessingStatusActivityInput(
                    message_id=message_id,
                    status=ProcessingStatus.UnifiedProcessingFailed,
                    message=f'All {total_document_tasks} files in batch failed processing due to corruption',
                    metadata={
                        'total_documents': total_document_tasks,
                        'failed_documents': failed_document_results,
                        'successful_documents': successful_document_results,
                        'error_reason': 'all_files_corrupted',
                    },
                ),
            )

            # Return error response
            error_response = UnifiedProcessingOutputFailed.model_validate(
                {
                    'message_id': message_id,
                    'status': ProcessingStatus.UnifiedProcessingFailed,
                    'error': f'All {total_document_tasks} files in batch failed processing due to corruption',
                    'signalr_user_id': input_data.signalr_user_id,
                }
            ).model_dump()

            return error_response

        # Log processing statistics for monitoring
        if failed_document_results > 0:
            logger.warning(
                f'Partial processing success: {successful_document_results}/{total_document_tasks} documents succeeded, {failed_document_results} failed'
            )
        else:
            logger.info(
                f'All processing successful: {successful_document_results} documents, prompt: {prompt_succeeded}'
            )

        # Pre-aggregate all document results into a single merged result
        if document_results:
            logger.info(f'Pre-aggregating {len(document_results)} document results')
            # Use the existing merger to combine multiple document results
            document_source_results = [(DataSourceType.DOCUMENTS, result) for result in document_results]
            merged_document_result = ExtractedDataMerger.merge_multi_source_results(document_source_results)

            # Save the merged document result to database
            save_extraction_data_output = yield context.call_activity(
                ActivityName.SaveExtractionData,
                SaveExtractionDataActivityInput(
                    message_id=message_id,
                    extraction_data=merged_document_result,
                    data_source_type=DataSourceType.DOCUMENTS,
                ),
            )
            logger.info(f'Saved merged document results: {merged_document_result}')
            yield from run_saved_extracted_data_post_processing(
                save_extraction_data_output=save_extraction_data_output,
                context=context,
                message_id=message_id,
                signalr_user_id=signalr_user_id,
                logger=logger,
            )

        # Save prompt result if it exists (since individual orchestrator skips saving when part of unified processing)
        if prompt_result:
            save_extraction_data_output: SaveExtractionDataActivityOutput = yield context.call_activity(
                ActivityName.SaveExtractionData,
                SaveExtractionDataActivityInput(
                    message_id=message_id,
                    extraction_data=prompt_result,
                    data_source_type=DataSourceType.PROMPT,
                ),
            )
            logger.info(f'Saved prompt results: {prompt_result}')
            yield from run_saved_extracted_data_post_processing(
                save_extraction_data_output=save_extraction_data_output,
                context=context,
                message_id=message_id,
                signalr_user_id=input_data.signalr_user_id,
                logger=logger,
            )

        # Prepare source results for final aggregation
        # Now we can safely retrieve from database since we've saved both merged document and prompt results
        source_results_for_aggregation = []
        if prompt_result:
            source_results_for_aggregation.append((DataSourceType.PROMPT, prompt_result.model_dump()))
        if (
            document_results and merged_document_result
        ):  # Only add if we had documents to process and merged_document_result is not None
            source_results_for_aggregation.append((DataSourceType.DOCUMENTS, merged_document_result.model_dump()))

        # Aggregate results from all sources
        aggregated_results: FinalExtractionDataResults = yield context.call_activity(
            ActivityName.AggregateMultiSourceData,
            AggregateMultiSourceDataActivityInput(
                message_id=message_id,
                source_results=source_results_for_aggregation,
            ),
        )

        # aggregated results contain concatenated objective_scopes and outcomes from all sources

        conflicts = yield context.call_activity(
            ActivityName.ExtractConflictsDetails,
            aggregated_results,
        )

        if conflicts:
            yield context.call_activity(
                ActivityName.SaveConflicts,
                SaveConflictsActivityInput(conflicts=conflicts, message_id=message_id),
            )

        logger.info(f'Data aggregation completed: {aggregated_results}')

        # Update status after aggregation
        yield context.call_activity(
            ActivityName.UpdateProcessingStatus,
            UpdateProcessingStatusActivityInput(
                message_id=message_id,
                status=ProcessingStatus.MultiSourceDataAggregated,
                message='Multi-source data aggregation completed',
                metadata={'aggregated_results': aggregated_results.model_dump()},
            ),
        )

        # Send notification for aggregation completion
        yield context.call_activity(
            ActivityName.SendNotification,
            SendNotificationActivityInput(
                event_type=EventType.MultiSourceDataAggregated,
                data={
                    'message_id': message_id,
                    'aggregated_results': aggregated_results.model_dump(),
                },
                signalr_user_id=input_data.signalr_user_id,
            ),
        )

        # Only send RequiredFieldsExtracted if NO documents are corrupted
        # This implements the core requirement: if ANY document is corrupted, don't send RequiredFieldsExtracted
        if aggregated_results and not documents_are_corrupted:
            logger.info('Sending RequiredFieldsExtracted event - no documents are corrupted')
            yield context.call_activity(
                ActivityName.SendNotification,
                SendNotificationActivityInput(
                    event_type=EventType.RequiredFieldsExtracted,
                    data={
                        'message_id': message_id,
                        'unified_processing': True,
                        'results': aggregated_results.model_dump(),
                        'sources': {
                            'text_prompt': input_data.text_prompt,
                            'documents': input_data.documents,
                        },
                    },
                    signalr_user_id=input_data.signalr_user_id,
                ),
            )
        elif documents_are_corrupted:
            logger.info('Skipping RequiredFieldsExtracted event - documents are corrupted')

        # Save aggregated results to blob storage
        blob_url: str = yield context.call_activity(
            ActivityName.SaveAggregatedResultsToBlob,
            SaveAggregatedResultsToBlobActivityInput(
                message_id=message_id,
                aggregated_data=aggregated_results,
            ),
        )

        # Update status after saving to blob
        yield context.call_activity(
            ActivityName.UpdateProcessingStatus,
            UpdateProcessingStatusActivityInput(
                message_id=message_id,
                status=ProcessingStatus.FinalResultsSavedToBlob,
                message='Final results saved to blob storage',
                metadata={'blob_url': blob_url},
            ),
        )

        # Send final queue message for further processing
        yield context.call_activity(
            ActivityName.SendFinalQueueMessage,
            SendFinalQueueMessageActivityInput(
                message_id=message_id,
                blob_url=blob_url,
                signalr_user_id=input_data.signalr_user_id,
            ),
        )

        # Update final status
        yield context.call_activity(
            ActivityName.UpdateProcessingStatus,
            UpdateProcessingStatusActivityInput(
                message_id=message_id,
                status=ProcessingStatus.UnifiedProcessingCompleted,
                message='Unified processing completed successfully',
                metadata={
                    'blob_url': blob_url,
                    'aggregated_results': aggregated_results.model_dump(),
                },
            ),
        )

        if failed_document_results == 0 and (not input_data.text_prompt or prompt_succeeded):
            # Send final notification with processing statistics
            yield context.call_activity(
                ActivityName.SendNotification,
                SendNotificationActivityInput(
                    event_type=EventType.UnifiedProcessingCompleted,
                    data={
                        'message_id': message_id,
                        'blob_url': blob_url,
                        'aggregated_results': aggregated_results.model_dump(),
                        'processing_stats': {
                            'total_documents': total_document_tasks,
                            'successful_documents': successful_document_results,
                            'failed_documents': failed_document_results,
                            'prompt_succeeded': prompt_succeeded,
                        },
                    },
                    signalr_user_id=input_data.signalr_user_id,
                ),
            )

        # Return success response
        response = UnifiedProcessingOutput.model_validate(
            {
                'message_id': message_id,
                'status': ProcessingStatus.UnifiedProcessingCompleted,
                'aggregated_results': aggregated_results.model_dump(),
                'blob_url': blob_url,
                'signalr_user_id': input_data.signalr_user_id,
            }
        ).model_dump()

        return response

    except Exception as e:
        logger.exception('Error in unified processing orchestrator')

        # Try to extract message_id for error reporting
        safe_message_id = None
        safe_signalr_user_id = None
        try:
            error_input_data = UnifiedProcessingInput.model_validate(input_dict)
            safe_signalr_user_id = error_input_data.signalr_user_id
            if error_input_data.text_prompt:
                safe_message_id, _ = parse_blob_url(error_input_data.text_prompt, is_prompt=True)
            elif error_input_data.documents:
                safe_message_id, _ = parse_blob_url(error_input_data.documents[0])
        except Exception:
            pass

        # Send error notifications if we have a message ID
        if safe_message_id and safe_signalr_user_id:
            try:
                yield context.call_activity(
                    ActivityName.UpdateProcessingStatus,
                    UpdateProcessingStatusActivityInput(
                        message_id=safe_message_id,
                        status=ProcessingStatus.UnifiedProcessingFailed,
                        message=f'Unified processing failed: {str(e)}',
                    ),
                )

                yield context.call_activity(
                    ActivityName.SendNotification,
                    SendNotificationActivityInput(
                        event_type=EventType.UnifiedProcessingFailed,
                        data={
                            'message_id': safe_message_id,
                            'error': str(e),
                        },
                        signalr_user_id=safe_signalr_user_id,
                    ),
                )
            except Exception:
                logger.exception('Error sending failure notifications')

        # Return error response
        error_response = UnifiedProcessingOutputFailed.model_validate(
            {
                'message_id': safe_message_id,
                'status': ProcessingStatus.UnifiedProcessingFailed,
                'error': str(e),
                'signalr_user_id': safe_signalr_user_id or 'unknown',
            }
        ).model_dump()

        return error_response
