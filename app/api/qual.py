import logging

from fastapi import APIRouter, Request, status

from constants.operation_ids import operation_ids
from dependencies import (
    ConversationServiceDep,
)
from dependencies.permissions import OwnerOnlyPermissionDep
from schemas import (
    CombinedExtractedDataResponse,
)
from schemas.conversation import QualExchangeResponse


__all__ = ['router']


logger = logging.getLogger(__name__)

router = APIRouter(prefix='/quals')


@router.get(
    '/{qual_id}/extracted-data-summary',
    operation_id=operation_ids.extracted_data_summary.GET_BY_QUAL_ID,
    response_model=CombinedExtractedDataResponse,
    status_code=status.HTTP_200_OK,
    dependencies=(OwnerOnlyPermissionDep,),
)
async def get_combined_extracted_data_by_qual_id(
    qual_id: str,
    request: Request,
    conversation_service: ConversationServiceDep,
) -> CombinedExtractedDataResponse:
    """
    Get combined extracted and aggregated data for a specific conversation by QualId.
    """
    # Extract token from Authorization header
    token = request.headers.get('Authorization', '').replace('Bearer ', '')

    return await conversation_service.get_combined_extracted_data_by_qual_id(qual_id, token)


@router.get(
    '/{qual_id}/conversation-id',
    operation_id=operation_ids.conversation.CONVERT_QUAL_ID_TO_CONV_ID,
    status_code=status.HTTP_200_OK,
    dependencies=(OwnerOnlyPermissionDep,),
)
async def convert_qual_id_to_conversation_id(
    qual_id: str,
    conversation_service: ConversationServiceDep,
) -> QualExchangeResponse:
    """
    Exchange a qual_id (str) for a conversation_id (UUID).
    """
    return await conversation_service.get_conversation_id_by_qual_id(qual_id)
