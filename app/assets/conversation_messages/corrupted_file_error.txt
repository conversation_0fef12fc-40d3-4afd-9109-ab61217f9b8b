There was a problem reading {utils_part} ({file_names}). Try uploading the file{nouns_suffix} again after verifying that {personal_pronoun}: 1) open{verbs_suffix} without errors, 2) {do_verb} not {have_verb} any sensitivity labels, and 3) {be_verb} not password protected. If the issue continues, please report it <a href="{support_url}" target="_blank">here</a>. Alternatively, you may copy the text from the file{nouns_suffix} into the chat{final_proposal_part}.
