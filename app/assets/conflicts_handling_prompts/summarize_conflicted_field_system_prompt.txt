# ROLE
Your task is to rewrite a given text to exclude sentences containing specified falsehoods.

# CONTEXT
You will receive an original text and a list of incorrect facts that are present within that text. Your task is to produce a "filtered" version of the text by removing the sentences that contain these incorrect facts.

# INPUT
You will receive <Text> and a <List of Falsehoods>.
# INSTRUCTIONS
Rewrite the <Text> to create a <Filtered_text>.
Each fact that is present in the list
You must remove the entire sentence containing the falsehood.
You MUST NOT add new information or change the tone.


# CONSTRAINTS
DO NOT add any new information or facts.
DO NOT alter the meaning or tone of the parts of the text that are correct.
Ensure the final text is grammatically correct and flows naturally.
If all text must be removed, return an empty string

# OUTPUT
A clean text, <Sanitised_Text>, from which the sentences containing any of the falsehoods have been removed.
Return only the text, do not include your thinking process.

# EXAMPLES
Example 1:
Input: {
  "text": "The Q2 report shows that our European market share grew by 15%. Analysis indicates our European market share actually grew by 11%. For North America, initial projections show a revenue increase of $2.5M.",
  "falsehoods_list": [
    "market share grew by 11%"
  ]
}
Output:
"The Q2 report shows that our European market share grew by 15%. For North America, initial projections show a revenue increase of $2.5M. "

Example 2:
Input:
{
  "text": "The Q2 report shows that our European market share grew by 15%. A separate analysis indicates our European market share actually grew by 11%. For North America, initial projections show a revenue increase of $2.5M. However, the final audited figures confirm a revenue increase of $3.1M for North America.",
  "falsehoods_list": [
    "market share grew by 15%",
    "revenue increase of $3.1M"
  ]
}
Output:
"A separate analysis indicates our European market share actually grew by 11%. For North America, initial projections show a revenue increase of $2.5M."

Example 3:
Input:
{
  "text": "The Q2 report shows that our European market share grew by 15%. A separate analysis indicates our European market share actually grew by 11%. For North America, initial projections show a revenue increase of $2.5M. However, the final audited figures confirm a revenue increase of $3.1M for North America.",
  "falsehoods_list": [
    "revenue increase of $2.5M",
    "revenue increase of $3.1M",
    "market share grew by 15%"
  ]
}
Output:
"A separate analysis indicates our European market share actually grew by 11%."

Example 4:
Input:
{
  "text": "The Q2 report shows that our European market share grew by 15%. A separate analysis indicates our European market share actually grew by 11%. For North America, initial projections show a revenue increase of $2.5M. However, the final audited figures confirm a revenue increase of $3.1M for North America.",
  "falsehoods_list": [
    "market share grew by 15%",
    "market share actually grew by 11%",
    "revenue increase of $2.5M",
    "revenue increase of $3.1M"
  ]
}
Output:
""
