from unittest.mock import AsyncMock, patch

from constants.message import MessageR<PERSON>, MessageType
from constants.operation_ids import operation_ids
from core.urls import URLResolver
from services.openai_client_name_validation import IsClientNameResponse, OpenAIClientNameValidationService


class TestClientNameValidationBugFix:
    """Test that the client name validation bug is fixed."""

    async def test_descriptive_text_no_longer_triggers_client_name_validation(
        self,
        auth_mock,
        auth_header,
        async_client,
        url_resolver: URLResolver,
        test_conversation_id,
    ):
        """
        Test that descriptive sentences like 'I worked on a big project'
        no longer trigger client name validation errors.

        This test reproduces the original bug scenario and verifies it's fixed.
        """
        # Mock OpenAI validation to return False for descriptive text
        with patch(
            'services.openai_client_name_validation.OpenAIClientNameValidationService.validate'
        ) as mock_validate:
            mock_validate.return_value = False
            # Send descriptive text that should NOT trigger client name validation
            message_url = url_resolver.reverse(operation_ids.message.CREATE)
            message_data = {
                'conversation_id': str(test_conversation_id),
                'role': MessageRole.USER,
                'message_type': MessageType.TEXT,
                'content': 'I worked on a big project',  # This should NOT be treated as client name
            }

            response = await async_client.post(message_url, headers=auth_header, data=message_data)

            # Should succeed (not return 500 error like before)
            assert response.status_code == 201

            response_data = response.json()

            # Should NOT contain invalid client name error
            # The system should process this as general conversation, not client name validation
            assert 'system' in response_data
            system_message = response_data['system']

            # Should not be an invalid client name error
            assert 'invalid client name' not in system_message.get('content', '').lower()
            assert 'not a valid client name' not in system_message.get('content', '').lower()

            # Validation should not have been called since this is general conversation
            # (not in client name input context)
            mock_validate.assert_not_called()

    async def test_validation_service_works_correctly(self):
        """
        Test that the OpenAI client name validation service works correctly.
        """

        # Create the service and verify it works
        openai_repo = AsyncMock()
        validation_service = OpenAIClientNameValidationService(openai_repository=openai_repo)

        # Mock the OpenAI response for invalid client name
        openai_repo.generate_chat_completion.return_value = IsClientNameResponse(is_client_name=False)

        # Test validation of descriptive text
        result = await validation_service.validate('I worked on a big project')
        assert result is False

        # Test validation of actual client name
        openai_repo.generate_chat_completion.return_value = IsClientNameResponse(is_client_name=True)
        result = await validation_service.validate('Microsoft Corporation')
        assert result is True

    async def test_various_descriptive_texts_validation(self):
        """
        Test that various types of descriptive text are correctly identified as invalid client names.
        """

        descriptive_texts = [
            'I worked on a big project',
            'Help me write my prompt',
            '$30 million cost saving. Increased revenue by 25% and improved customer satisfaction by 12%.',
            'This is a descriptive sentence about my work',
            'Can you help me with this task?',
            'I need assistance with my project',
        ]

        # Create the service
        openai_repo = AsyncMock()
        validation_service = OpenAIClientNameValidationService(openai_repository=openai_repo)

        # Mock OpenAI to return False for descriptive texts
        openai_repo.generate_chat_completion.return_value = IsClientNameResponse(is_client_name=False)

        for text in descriptive_texts:
            result = await validation_service.validate(text)
            assert result is False, f'Text should be invalid client name: {text}'

        # Test that actual client names return True
        openai_repo.generate_chat_completion.return_value = IsClientNameResponse(is_client_name=True)

        valid_client_names = [
            'Microsoft Corporation',
            'Apple Inc.',
            'Google LLC',
            'Amazon.com Inc.',
        ]

        for client_name in valid_client_names:
            result = await validation_service.validate(client_name)
            assert result is True, f'Client name should be valid: {client_name}'
