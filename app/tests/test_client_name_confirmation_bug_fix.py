"""
Test for client name confirmation bug fix.

This test verifies that both "Yes" and "Yes, this is correct" confirmation responses
correctly use the extracted client name instead of the entire user message.
"""

from datetime import datetime
from unittest.mock import AsyncMock, patch
from uuid import uuid4

import pytest

from constants.extracted_data import ConversationState
from constants.message import (
    ConversationMessageIntention,
    MessageRole,
    MessageType,
    SystemReplyType,
)
from schemas import AggregatedData, ConfirmedData, ConversationData, UserMessageSerializer
from services.message_processor import ConversationMessageProcessor


class TestClientNameConfirmationBugFix:
    """Test client name confirmation bug fix."""

    @pytest.mark.parametrize(
        'confirmation_response,expected_client_name',
        [
            ('Yes', 'MSD International GmbH (Singapore Branch)'),
            ('yes', 'MSD International GmbH (Singapore Branch)'),
            ('yES', 'MSD International GmbH (Singapore Branch)'),
            ('Yes, this is correct', 'MSD International GmbH (Singapore Branch)'),
            ('yes, this is correct', 'MSD International GmbH (Singapore Branch)'),
            ('YES, this is correct', 'MSD International GmbH (Singapore Branch)'),
        ],
    )
    async def test_client_name_confirmation_uses_extracted_name_not_full_prompt(
        self,
        confirmation_response: str,
        expected_client_name: str,
        test_conversation_id,
        auth_token,
    ):
        """
        Test that when user confirms with "Yes" or "Yes, this is correct",
        the system uses the extracted client name, not the entire original prompt.

        This test addresses the bug where "Yes" confirmation was incorrectly
        using the entire user message as the client name.
        """
        # Simulate the original long prompt that contains client details
        original_prompt = (
            'MSD International GmbH (Singapore Branch) wanted to conduct a culture discovery '
            'to understand the key drivers that led to multiple workplace misconducts. '
            'The client engaged Deloitte to build a stronger culture of integrity and respect '
            'through embedment of their core values that emphasize on mutual respect, inclusion '
            'and accountability. Deloitte embarked on a four-week journey to Discover the '
            "client's Culture from 16 August 2024 to 13 December 2024."
        )

        # Create user message with confirmation response
        user_message = UserMessageSerializer(
            id=uuid4(),
            conversation_id=test_conversation_id,
            role=MessageRole.USER,
            type=MessageType.TEXT,
            content=confirmation_response,
            created_at=datetime.now(),
            selected_option=None,
        )

        # Mock aggregated data with extracted client name
        mock_aggregated_data = AggregatedData(
            client_name=['MSD International GmbH (Singapore Branch)'],
            ldmf_country=[],
            date_intervals=[],
            date_intervals_original=[],
            objective_and_scope=None,
            outcomes=None,
        )

        # Mock confirmed data (empty initially)
        mock_confirmed_data = ConfirmedData()

        # Mock conversation in COLLECTING_CLIENT_NAME state
        mock_conversation = AsyncMock()
        mock_conversation.State = ConversationState.COLLECTING_CLIENT_NAME

        with (
            patch('services.message_processor.ConversationMessageProcessor._get_intent') as mock_get_intent,
            patch(
                'services.message_processor.ConversationMessageProcessor._handle_manual_input'
            ) as mock_handle_manual_input,
        ):
            # Setup mocks
            mock_get_intent.return_value = ConversationMessageIntention.USER_CONFIRMATION
            mock_handle_manual_input.return_value = True  # Client name was confirmed

            # Create mock services
            mock_intent_classifier = AsyncMock()
            mock_extracted_data_service = AsyncMock()
            mock_conversation_repository = AsyncMock()
            mock_date_validator = AsyncMock()
            mock_document_service = AsyncMock()
            mock_conversation_message_repository = AsyncMock()

            # Setup mock returns
            mock_conversation_repository.get.return_value = mock_conversation
            mock_conversation_repository.get_confirmed_data.return_value = mock_confirmed_data
            mock_extracted_data_service.aggregate_data.return_value = mock_aggregated_data

            # Create processor instance
            processor = ConversationMessageProcessor(
                conversation_id=test_conversation_id,
                conversation_data=ConversationData(
                    conversation_message_history=[],
                    aggregated_data=mock_aggregated_data,
                    confirmed_data=mock_confirmed_data,
                    conversation=mock_conversation,
                ),
                user_message=user_message,
                files=None,
                intent_classifier_service=mock_intent_classifier,
                extracted_data_service=mock_extracted_data_service,
                conversation_repository=mock_conversation_repository,
                conversation_message_repository=mock_conversation_message_repository,
                date_validator_service=mock_date_validator,
                document_service=mock_document_service,
                openai_client_name_validation_service=AsyncMock(),
                conversation_message_history=[],
                token=auth_token,
            )

            # Process the confirmation message
            result = await processor.run()
            expected_reply_type = SystemReplyType.CLIENT_NAME_CONFIRMED

            # Verify the result
            assert result.intention == ConversationMessageIntention.USER_CONFIRMATION
            assert result.system_reply_type == expected_reply_type

            # The key assertion: verify the system reply uses the extracted client name,
            # not the confirmation response or original prompt
            expected_reply = expected_reply_type.message_text.format(client_name=expected_client_name)
            assert expected_reply == result.system_reply

            # Verify the confirmation response itself is NOT used as the client name
            assert confirmation_response not in result.system_reply
            assert original_prompt not in result.system_reply

            # Most importantly: verify the extracted client name IS used
            assert expected_client_name in result.system_reply

    async def test_manual_client_name_entry_still_works(
        self,
        test_conversation_id,
        auth_token,
    ):
        """
        Test that manual client name entry (not confirmation) still works correctly.
        This ensures our fix doesn't break the case where user types a new client name.
        """
        manual_client_name = 'New Client Corporation'

        user_message = UserMessageSerializer(
            id=uuid4(),
            conversation_id=test_conversation_id,
            role=MessageRole.USER,
            type=MessageType.TEXT,
            content=manual_client_name,
            created_at=datetime.now(),
            selected_option=None,
        )

        # Mock aggregated data with different client name
        mock_aggregated_data = AggregatedData(
            client_name=['Different Client Name'],
            ldmf_country=[],
            date_intervals=[],
            date_intervals_original=[],
            objective_and_scope=None,
            outcomes=None,
        )

        mock_confirmed_data = ConfirmedData()
        mock_conversation = AsyncMock()
        mock_conversation.State = ConversationState.COLLECTING_CLIENT_NAME

        with (
            patch('services.message_processor.ConversationMessageProcessor._get_intent') as mock_get_intent,
            patch(
                'services.message_processor.ConversationMessageProcessor._handle_manual_input'
            ) as mock_handle_manual_input,
        ):
            # Setup mocks - manual input returns True (client name was confirmed)
            mock_get_intent.return_value = ConversationMessageIntention.USER_CONFIRMATION
            mock_handle_manual_input.return_value = True

            # Create mock services
            mock_intent_classifier = AsyncMock()
            mock_extracted_data_service = AsyncMock()
            mock_conversation_repository = AsyncMock()
            mock_date_validator = AsyncMock()
            mock_document_service = AsyncMock()
            mock_conversation_message_repository = AsyncMock()

            # Setup mock returns
            mock_conversation_repository.get.return_value = mock_conversation
            mock_conversation_repository.get_confirmed_data.return_value = mock_confirmed_data
            mock_extracted_data_service.aggregate_data.return_value = mock_aggregated_data

            processor = ConversationMessageProcessor(
                conversation_id=test_conversation_id,
                conversation_data=ConversationData(
                    conversation_message_history=[],
                    aggregated_data=mock_aggregated_data,
                    confirmed_data=mock_confirmed_data,
                    conversation=mock_conversation,
                ),
                user_message=user_message,
                files=None,
                intent_classifier_service=mock_intent_classifier,
                extracted_data_service=mock_extracted_data_service,
                conversation_repository=mock_conversation_repository,
                conversation_message_repository=mock_conversation_message_repository,
                date_validator_service=mock_date_validator,
                document_service=mock_document_service,
                openai_client_name_validation_service=AsyncMock(),
                conversation_message_history=[],
                token=auth_token,
            )

            result = await processor.run()
            expected_reply_type = SystemReplyType.CLIENT_NAME_CONFIRMED

            # For manual entry, the user's input should be used as the client name
            expected_reply = expected_reply_type.message_text.format(client_name=manual_client_name)
            assert expected_reply == result.system_reply

            # Verify the manual client name is used in the response
            assert manual_client_name in result.system_reply
