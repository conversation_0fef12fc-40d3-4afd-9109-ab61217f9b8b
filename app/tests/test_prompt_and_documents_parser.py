from datetime import date, datetime
from unittest.mock import patch
from uuid import UUID

import pytest

from constants.extracted_data import DataSourceType
from schemas import ExtractedData
from services.extracted_data.parsers.prompt_and_documents import PromptAndDocumentDataParser


class TestPromptAndDocumentDataParser:
    @pytest.fixture
    def initial_extracted_data(self):
        return ExtractedData(
            ConversationPublicId=UUID('123e4567-e89b-12d3-a456-************'),
            DataSourceType=DataSourceType.PROMPT,
            CreatedAt=datetime.fromisoformat('2023-01-01T00:00:00Z'),
        )

    def test_call_with_all_fields(self, initial_extracted_data):
        document_data = {
            'client_name': ['Client X'],
            'ldmf_country': ['USA'],
            'title': 'Project Title',
            'start_date': '2023-01-15',
            'end_date': '2023-01-30',
            'objective_and_scope': 'Objective and Scope details',
            'outcomes': 'Project Outcomes',
        }
        updated_data: ExtractedData = PromptAndDocumentDataParser()(initial_extracted_data, document_data)

        assert updated_data.client_name == ['Client X']
        assert updated_data.ldmf_country == ['USA']
        assert updated_data.title == 'Project Title'
        assert updated_data.start_date == date(2023, 1, 15)
        assert updated_data.end_date == date(2023, 1, 30)
        assert updated_data.objective_and_scope == 'Objective and Scope details'
        assert updated_data.outcomes == 'Project Outcomes'

    def test_call_with_no_fields(self, initial_extracted_data):
        document_data = {}
        updated_data = PromptAndDocumentDataParser()(initial_extracted_data, document_data)

        assert updated_data.client_name == []
        assert updated_data.ldmf_country == []
        assert updated_data.title is None
        assert updated_data.start_date is None
        assert updated_data.end_date is None
        assert updated_data.objective_and_scope is None
        assert updated_data.outcomes is None

    def test_call_with_partial_fields(self, initial_extracted_data):
        document_data = {
            'client_name': ['Client Y'],
            'title': 'Another Project',
        }
        updated_data = PromptAndDocumentDataParser()(initial_extracted_data, document_data)

        assert updated_data.client_name == ['Client Y']
        assert updated_data.ldmf_country == []
        assert updated_data.title == 'Another Project'
        assert updated_data.start_date is None
        assert updated_data.end_date is None
        assert updated_data.objective_and_scope is None
        assert updated_data.outcomes is None

    @pytest.mark.parametrize(
        'input_val, expected_date',
        [
            (None, None),
            (date(2024, 2, 1), date(2024, 2, 1)),
            ('2023-03-01', date(2023, 3, 1)),
        ],
    )
    def test_get_date_for_extracted_data_valid_inputs(self, input_val, expected_date):
        result = PromptAndDocumentDataParser._get_date_for_extracted_data(input_val)
        assert result == expected_date

    @patch('services.extracted_data.parsers.prompt_and_documents.logger')
    def test_get_date_for_extracted_data_invalid_string(self, mock_logger):
        result = PromptAndDocumentDataParser._get_date_for_extracted_data('invalid-date')
        assert result is None
        mock_logger.warning.assert_called_once_with(
            'A document date of an unexpected format was detected: %s', 'invalid-date'
        )

    @patch('services.extracted_data.parsers.prompt_and_documents.logger')
    def test_get_date_for_extracted_data_unexpected_type(self, mock_logger):
        result = PromptAndDocumentDataParser._get_date_for_extracted_data(12345)
        assert result is None
        mock_logger.warning.assert_called_once_with(
            'A document date of an unexpected type was detected: %s', type(12345)
        )
