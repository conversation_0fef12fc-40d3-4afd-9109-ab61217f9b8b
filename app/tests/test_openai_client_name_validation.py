from datetime import datetime
from unittest.mock import AsyncMock
from uuid import uuid4

import pytest

from constants.extracted_data import ConversationState, MissingDataStatus
from constants.message import MessageRole, MessageType, SystemReplyType
from schemas import AggregatedData, ConversationData
from schemas.confirmed_data import ConfirmedData
from schemas.conversation_message.message import UserMessageSerializer
from services.message_processor import ConversationMessageProcessor
from services.openai_client_name_validation import IsClientNameResponse, OpenAIClientNameValidationService


class TestOpenAIClientNameValidation:
    """Test OpenAI client name validation service."""

    @pytest.fixture
    def openai_repository_mock(self):
        return AsyncMock()

    @pytest.fixture
    def validation_service(self, openai_repository_mock):
        return OpenAIClientNameValidationService(openai_repository=openai_repository_mock)

    async def test_validate_valid_client_name(self, validation_service, openai_repository_mock):
        """Test validation of a valid client name."""
        # Mock OpenAI response
        openai_repository_mock.generate_chat_completion.return_value = IsClientNameResponse(is_client_name=True)

        result = await validation_service.validate('Microsoft Corporation')

        assert result is True
        openai_repository_mock.generate_chat_completion.assert_called_once()

    async def test_validate_invalid_client_name(self, validation_service, openai_repository_mock):
        """Test validation of invalid descriptive text."""
        # Mock OpenAI response
        openai_repository_mock.generate_chat_completion.return_value = IsClientNameResponse(is_client_name=False)

        result = await validation_service.validate('I worked on a big project')

        assert result is False
        openai_repository_mock.generate_chat_completion.assert_called_once()

    async def test_validate_handles_exception(self, validation_service, openai_repository_mock):
        """Test that validation handles exceptions gracefully."""
        # Mock OpenAI to raise an exception
        openai_repository_mock.generate_chat_completion.side_effect = Exception('API Error')

        result = await validation_service.validate('Test Client')

        assert result is False

    async def test_validate_handles_invalid_response_type(self, validation_service, openai_repository_mock):
        """Test that validation handles invalid response types."""
        # Mock OpenAI to return unexpected response type
        openai_repository_mock.generate_chat_completion.return_value = 'invalid response'

        result = await validation_service.validate('Test Client')

        assert result is False


class TestClientNameValidationIntegration:
    """Test client name validation integration in message processor."""

    @pytest.fixture
    def mock_conversation_data(self):
        return ConversationData(
            conversation_message_history=[],
            aggregated_data=AggregatedData(),
            confirmed_data=ConfirmedData(),
            conversation=type('MockConversation', (), {'id': uuid4(), 'State': 'collecting_client_name'})(),
        )

    @pytest.fixture
    def mock_user_message(self):
        return UserMessageSerializer(
            conversation_id=uuid4(),
            role=MessageRole.USER,
            type=MessageType.TEXT,
            content='Test message',
            created_at=datetime.now(),
            id=uuid4(),
            selected_option=None,
        )

    async def test_handle_client_name_input_valid_name(self, mock_conversation_data, mock_user_message):
        """Test that valid client names pass validation and continue processing."""
        mock_validation_service = AsyncMock()
        mock_validation_service.validate.return_value = True

        mock_extracted_data_service = AsyncMock()
        mock_extracted_data_service.quals_clients_repository.search_clients.return_value = AsyncMock(clients=[])

        processor = ConversationMessageProcessor(
            conversation_id=mock_conversation_data.conversation.id,
            user_message=mock_user_message,
            intent_classifier_service=AsyncMock(),
            extracted_data_service=mock_extracted_data_service,
            conversation_repository=AsyncMock(),
            conversation_message_repository=AsyncMock(),
            date_validator_service=AsyncMock(),
            document_service=AsyncMock(),
            openai_client_name_validation_service=mock_validation_service,
            conversation_message_history=[],
            conversation_data=mock_conversation_data,
        )

        await processor._handle_client_name_input('Microsoft Corporation', 'test')

        # Should call validation
        mock_validation_service.validate.assert_called_once_with('Microsoft Corporation')
        # Should continue to API search since validation passed (may be called multiple times due to fallback logic)
        assert mock_extracted_data_service.quals_clients_repository.search_clients.call_count >= 1

    async def test_handle_client_name_input_invalid_name(self, mock_conversation_data, mock_user_message):
        """Test that invalid client names are rejected with appropriate error."""
        mock_validation_service = AsyncMock()
        mock_validation_service.validate.return_value = False

        mock_extracted_data_service = AsyncMock()

        processor = ConversationMessageProcessor(
            conversation_id=mock_conversation_data.conversation.id,
            user_message=mock_user_message,
            intent_classifier_service=AsyncMock(),
            extracted_data_service=mock_extracted_data_service,
            conversation_repository=AsyncMock(),
            conversation_message_repository=AsyncMock(),
            date_validator_service=AsyncMock(),
            document_service=AsyncMock(),
            openai_client_name_validation_service=mock_validation_service,
            conversation_message_history=[],
            conversation_data=mock_conversation_data,
        )

        result = await processor._handle_client_name_input('I worked on a big project', 'test')

        # Should call validation
        mock_validation_service.validate.assert_called_once_with('I worked on a big project')
        # Should NOT continue to API search since validation failed
        mock_extracted_data_service.quals_clients_repository.search_clients.assert_not_called()

        # Should return invalid client name error
        assert result.system_reply_type == SystemReplyType.INVALID_CLIENT_NAME
        assert result.missing_data_status == MissingDataStatus.MISSING_DATA
        assert result.conversation_state == ConversationState.COLLECTING_CLIENT_NAME
        assert result.system_reply == SystemReplyType.INVALID_CLIENT_NAME.message_text

    async def test_handle_client_name_input_validation_before_length_check(
        self, mock_conversation_data, mock_user_message
    ):
        """Test that validation occurs after length check."""
        mock_validation_service = AsyncMock()
        mock_validation_service.validate.return_value = False

        processor = ConversationMessageProcessor(
            conversation_id=mock_conversation_data.conversation.id,
            user_message=mock_user_message,
            intent_classifier_service=AsyncMock(),
            extracted_data_service=AsyncMock(),
            conversation_repository=AsyncMock(),
            conversation_message_repository=AsyncMock(),
            date_validator_service=AsyncMock(),
            document_service=AsyncMock(),
            openai_client_name_validation_service=mock_validation_service,
            conversation_message_history=[],
            conversation_data=mock_conversation_data,
        )

        # Test with very long string (should fail length check first)
        long_string = 'a' * 150  # Exceeds MAX_CLIENT_NAME_LENGTH
        result = await processor._handle_client_name_input(long_string, 'test')

        # Should NOT call validation for overly long strings
        mock_validation_service.validate.assert_not_called()
        # Should return length error instead
        assert result.system_reply_type == SystemReplyType.CLIENT_NAME_TOO_LONG

    @pytest.mark.parametrize(
        'invalid_input,expected_validation_call',
        [
            ('I worked on a big project', True),
            ('Help me write my prompt', True),
            ('This is a descriptive sentence about my work', True),
            ('$30 million cost saving project', True),
            ('A small startup company', True),
        ],
    )
    async def test_various_invalid_inputs(
        self, mock_conversation_data, mock_user_message, invalid_input, expected_validation_call
    ):
        """Test various types of invalid input that should be caught by validation."""
        mock_validation_service = AsyncMock()
        mock_validation_service.validate.return_value = False

        processor = ConversationMessageProcessor(
            conversation_id=mock_conversation_data.conversation.id,
            user_message=mock_user_message,
            intent_classifier_service=AsyncMock(),
            extracted_data_service=AsyncMock(),
            conversation_repository=AsyncMock(),
            conversation_message_repository=AsyncMock(),
            date_validator_service=AsyncMock(),
            document_service=AsyncMock(),
            openai_client_name_validation_service=mock_validation_service,
            conversation_message_history=[],
            conversation_data=mock_conversation_data,
        )

        result = await processor._handle_client_name_input(invalid_input, 'test')

        if expected_validation_call:
            mock_validation_service.validate.assert_called_once_with(invalid_input)
            assert result.system_reply_type == SystemReplyType.INVALID_CLIENT_NAME
        else:
            mock_validation_service.validate.assert_not_called()
