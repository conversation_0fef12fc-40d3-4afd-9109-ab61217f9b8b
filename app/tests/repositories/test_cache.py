import asyncio

import pytest

from repositories.cache import InMemoryCacheRepository


class TestInMemoryCacheRepository:
    """Test suite for InMemoryCacheRepository."""

    @pytest.fixture
    def cache_repo(self) -> InMemoryCacheRepository:
        """Create a cache repository instance for testing."""
        return InMemoryCacheRepository(
            maxsize=5,
            ttl=1,  # 1 second TTL for fast testing
            key_prefix='test:',
        )

    async def test_basic_set_and_get(self, cache_repo: InMemoryCacheRepository):
        """Test basic set and get operations."""
        # Test string value
        await cache_repo.set('key1', 'value1')
        result = await cache_repo.get('key1')
        assert result == 'value1'

        # Test integer value
        await cache_repo.set('key2', 42)
        result = await cache_repo.get('key2')
        assert result == 42

        # Test None value
        await cache_repo.set('key3', None)
        result = await cache_repo.get('key3')
        assert result is None

    async def test_get_nonexistent_key(self, cache_repo: InMemoryCacheRepository):
        """Test getting a non-existent key returns None."""
        result = await cache_repo.get('nonexistent')
        assert result is None

    async def test_complex_object_serialization(self, cache_repo: InMemoryCacheRepository):
        """Test serialization and deserialization of complex objects."""
        complex_obj = {
            'name': 'test',
            'data': [1, 2, 3],
            'nested': {'key': 'value'},
        }

        await cache_repo.set('complex', complex_obj)
        result = await cache_repo.get('complex')
        assert result == complex_obj

    async def test_serialization_disabled(self, cache_repo: InMemoryCacheRepository):
        """Test that complex objects are stored as-is when serialization is disabled."""
        complex_obj = {'name': 'test', 'data': [1, 2, 3]}

        await cache_repo.set('complex', complex_obj)
        result = await cache_repo.get('complex')
        assert result == complex_obj
        assert result is complex_obj  # Should be the same object reference

    async def test_ttl_expiration(self, cache_repo: InMemoryCacheRepository):
        """Test that items expire after TTL."""
        await cache_repo.set('expiring_key', 'value')

        # Should be available immediately
        result = await cache_repo.get('expiring_key')
        assert result == 'value'

        # Wait for expiration (TTL is 1 second)
        await asyncio.sleep(1.1)

        # Should be expired now
        result = await cache_repo.get('expiring_key')
        assert result is None

    async def test_key_prefix(self, cache_repo: InMemoryCacheRepository):
        """Test that key prefix is applied correctly."""
        await cache_repo.set('test_key', 'value')

        # The actual key in the cache should have the prefix
        prefixed_key = cache_repo._make_key('test_key')
        assert prefixed_key == 'test:test_key'

        # But we should be able to retrieve it with the original key
        result = await cache_repo.get('test_key')
        assert result == 'value'

    async def test_error_handling_in_get(self, cache_repo: InMemoryCacheRepository):
        """Test error handling in get method."""
        # This test is more about ensuring the method doesn't crash
        # In a real scenario, we might mock the cache to raise an exception
        result = await cache_repo.get('any_key')
        assert result is None  # Should handle gracefully

    async def test_serialization_error_handling(self, cache_repo: InMemoryCacheRepository):
        """Test handling of serialization errors."""

        # Create an object that can't be JSON serialized
        class UnserializableClass:
            def __init__(self):
                self.circular_ref = self

        unserializable_obj = UnserializableClass()

        # Should not raise an exception, but might store the object as-is
        await cache_repo.set('unserializable', unserializable_obj)
        result = await cache_repo.get('unserializable')

        # The exact behavior depends on implementation, but it shouldn't crash
        assert result is not None
