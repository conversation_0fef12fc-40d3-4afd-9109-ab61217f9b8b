from pathlib import Path

from alembic import command
from alembic.config import Config
import pytest_asyncio
import sqlalchemy as sa
from sqlalchemy.ext.asyncio import AsyncSession, async_sessionmaker, create_async_engine
from sqlalchemy.pool import NullPool

from config import settings


__all__ = ['setup_test_db', 'db_engine', 'db_session']


async def _create_test_database():
    """Create test database."""
    default_engine = create_async_engine(
        settings.db.uri.replace(f'DATABASE={settings.db.name}', 'DATABASE=master'),
        isolation_level='AUTOCOMMIT',  # This is crucial for DDL operations
    )

    async with default_engine.connect() as conn:
        await conn.execute(
            sa.text(
                f"IF NOT EXISTS (SELECT * FROM sys.databases WHERE name = N'{settings.db.name}') "
                f'BEGIN CREATE DATABASE [{settings.db.name}] END'
            )
        )

    await default_engine.dispose()


def _run_migrations():
    """Run alembic migrations."""
    alembic_cfg = Config(Path(__file__).parents[2] / 'alembic.ini')
    command.upgrade(alembic_cfg, 'head')


async def _drop_test_database():
    """Drop test database."""
    default_engine = create_async_engine(
        settings.db.uri.replace(f'DATABASE={settings.db.name}', 'DATABASE=master'),
        isolation_level='AUTOCOMMIT',  # This is crucial for DDL operations
    )

    async with default_engine.connect() as conn:
        # Execute each command separately
        await conn.execute(
            sa.text(
                f"IF EXISTS (SELECT * FROM sys.databases WHERE name = N'{settings.db.name}') "
                f'BEGIN ALTER DATABASE [{settings.db.name}] SET SINGLE_USER WITH ROLLBACK IMMEDIATE END'
            )
        )
        await conn.execute(sa.text(f'DROP DATABASE IF EXISTS [{settings.db.name}]'))

    await default_engine.dispose()


@pytest_asyncio.fixture(scope='session', autouse=True)
async def setup_test_db():
    """Setup test database and run migrations."""
    await _drop_test_database()
    await _create_test_database()
    _run_migrations()
    yield
    await _drop_test_database()


@pytest_asyncio.fixture(scope='session')
async def db_engine(setup_test_db):  # Depends on setup_test_db to ensure DB exists
    """Provide a SQLAlchemy async engine for the test session."""
    engine = create_async_engine(
        settings.db.uri,
        echo=False,  # Set to True for debugging SQL queries
        poolclass=NullPool,  # Use NullPool for testing to ensure connections are not reused across transactions
    )
    yield engine
    await engine.dispose()


@pytest_asyncio.fixture()
async def db_session(db_engine):
    """Provide a transactional scoped session for each test function."""
    connection = await db_engine.connect()
    transaction = await connection.begin()
    session_maker = async_sessionmaker(
        bind=connection,
        class_=AsyncSession,
        expire_on_commit=False,  # Keep objects associated with the session after commit
        autocommit=False,
        autoflush=False,
    )
    async with session_maker() as session:
        yield session
        # Rollback the transaction after each test to ensure test isolation
        await transaction.rollback()
        await connection.close()
