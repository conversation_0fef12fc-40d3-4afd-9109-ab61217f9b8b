from unittest.mock import AsyncMock, patch

import pytest


__all__ = [
    'mock_fee_and_currency_repo',
]


@pytest.fixture(autouse=True)
def mock_fee_and_currency_repo():
    """Auto-use fixture that mocks FeeAndCurrencyRepository to prevent network calls."""
    with (
        patch(
            'repositories.fee_and_currency.FeeAndCurrencyRepository.get_project_fee_display_options',
            new_callable=AsyncMock,
        ) as mock_project_fee_display_options,
        patch(
            'repositories.fee_and_currency.FeeAndCurrencyRepository.get_currencies', new_callable=AsyncMock
        ) as mock_currencies,
    ):
        mock_project_fee_display_options.return_value = []
        mock_currencies.return_value = []
        yield
