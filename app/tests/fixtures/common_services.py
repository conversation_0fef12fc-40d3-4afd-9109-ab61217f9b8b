from unittest.mock import AsyncMock, patch

import pytest


__all__ = [
    'mock_ldmf_country_service_list',
    'mock_industry_data_service_list',
    'mock_role_data_service_list',
    'mock_client_service_service_list',
]


@pytest.fixture(autouse=True)
def mock_ldmf_country_service_list(request):
    """Auto-mock for LDMFCountryService.list to prevent network calls."""
    if 'disable_autouse' in request.keywords:
        yield
        return
    with patch('services.ldmf_country.LDMFCountryService.list', new_callable=AsyncMock) as mock:
        mock.return_value = []
        yield mock


@pytest.fixture(autouse=True)
def mock_industry_data_service_list(request):
    """Auto-mock for ClientIndustryDataService.list to prevent network calls."""
    if 'disable_autouse' in request.keywords:
        yield
        return
    with patch('services.client_industry.ClientIndustryDataService.list', new_callable=AsyncMock) as mock:
        mock.return_value = []
        yield mock


@pytest.fixture(autouse=True)
def mock_role_data_service_list(request):
    """Auto-mock for RoleDataService.list to prevent network calls."""
    if 'disable_autouse' in request.keywords:
        yield
        return
    with patch('services.project_role.RoleDataService.list', new_callable=AsyncMock) as mock:
        mock.return_value = {}
        yield mock


@pytest.fixture(autouse=True)
def mock_client_service_service_list(request):
    """Auto-mock for ClientServiceDataService.list to prevent network calls."""
    if 'disable_autouse' in request.keywords:
        yield
        return
    with patch('services.client_service.ClientServiceDataService.list', new_callable=AsyncMock) as mock:
        mock.return_value = []
        yield mock
