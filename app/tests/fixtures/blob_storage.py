from azure.storage.blob.aio import BlobServiceClient
import pytest_asyncio

from config import settings


__all__ = ['drop_containers_before_each_test', 'drop_containers_on_tests_complete']


async def _drop_containers():
    async with BlobServiceClient.from_connection_string(settings.blob_storage.connection_string) as service_client:
        for container_name in (
            settings.blob_storage.default_container_name,
            settings.blob_storage.document_container_name,
        ):
            container_client = service_client.get_container_client(container_name)
            if await container_client.exists():
                await container_client.delete_container()


@pytest_asyncio.fixture(autouse=True)
async def drop_containers_before_each_test():
    await _drop_containers()
    yield


@pytest_asyncio.fixture(autouse=True, scope='session')
async def drop_containers_on_tests_complete():
    yield
    await _drop_containers()
