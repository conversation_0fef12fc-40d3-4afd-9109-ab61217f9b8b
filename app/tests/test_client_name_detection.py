"""
Tests for client name detection and confirmation flow.
"""

from datetime import datetime
from unittest.mock import AsyncMock, patch
from uuid import UUID

from fastapi import status
from pydantic import ValidationError
import pytest

from constants.message import (
    ConversationMessageIntention,
    MessageRole,
    MessageType,
)
from constants.operation_ids import operation_ids
from core.http_client import CustomAsyncClient
from schemas import ClientSearchItem, ClientSearchResponse, ConfirmedData, UserMessageSerializer
from services.message_processor import ConversationMessageProcessor


class TestClientNameDetection:
    """Test client name detection and confirmation flow."""

    async def test_single_client_match_auto_confirmation(
        self,
        auth_header,
        async_client: CustomAsyncClient,
        url_resolver,
        test_conversation_id,
    ):
        """
        Test that when exactly ONE client match is found, it's automatically confirmed.
        """
        user_message = 'Microsoft'

        with (
            patch(
                'services.message_processor.ConversationMessageProcessor._get_intent',
                new_callable=AsyncMock,
                return_value=ConversationMessageIntention.EXTRACTION,
            ),
            patch('repositories.conversation.ConversationRepository.get') as mock_get_conversation,
            patch('repositories.conversation.ConversationRepository.get_confirmed_data') as mock_get_confirmed_data,
            patch('repositories.quals_clients.QualsClientsRepository.search_clients') as mock_search,
            patch('services.ldmf_country.LDMFCountryService.list', new_callable=AsyncMock) as mock_list,
            patch('services.client_industry.ClientIndustryDataService.list') as mock_industry_list,
            patch('services.client_service.ClientServiceDataService.list') as mock_service_list,
            patch('services.engagement_location.EngagementLocationDataService.list') as mock_engagement_locations_list,
        ):
            # Set up mocks
            mock_conversation = type(
                'MockConversation',
                (),
                {'id': test_conversation_id, 'State': 'collecting_client_name', 'StateContext': {}},
            )
            mock_get_conversation.return_value = mock_conversation
            mock_get_confirmed_data.return_value = ConfirmedData()

            # Mock single client search result
            mock_search.return_value = ClientSearchResponse(
                clients=[ClientSearchItem(id=1, name='Microsoft Corporation', qualsCount=50, clientConfidentiality=1)],
                total_count=1,
                page_size=5,
                page_idx=0,
                exact_match=False,
            )

            # Mock LDMF country service
            mock_list.return_value = {}

            # Mock industry list to return empty list
            mock_industry_list.return_value = []

            # Mock service list to return empty list
            mock_service_list.return_value = []

            # Mock engagement locations list to return empty list
            mock_engagement_locations_list.return_value = []

            message_url = url_resolver.reverse(operation_ids.message.CREATE)
            message_data = {
                'conversation_id': str(test_conversation_id),
                'role': MessageRole.USER,
                'message_type': MessageType.TEXT,
                'content': user_message,
            }

            message_response = await async_client.post(message_url, headers=auth_header, data=message_data)

        assert message_response.status_code == status.HTTP_201_CREATED
        data = message_response.json()

        # Should not generate system message
        assert 'system' in data
        assert data['system'] is None

    async def test_single_client_match_requires_confirmation(
        self,
        auth_header,
        async_client: CustomAsyncClient,
        url_resolver,
        test_conversation_id,
    ):
        """
        Test that when multiple matches are found, user confirmation is required.
        """
        user_message = 'Apple'

        with (
            patch(
                'services.message_processor.ConversationMessageProcessor._get_intent',
                new_callable=AsyncMock,
                return_value=ConversationMessageIntention.EXTRACTION,
            ),
            patch('repositories.conversation.ConversationRepository.get') as mock_get_conversation,
            patch('repositories.conversation.ConversationRepository.get_confirmed_data') as mock_get_confirmed_data,
            patch('repositories.quals_clients.QualsClientsRepository.search_clients') as mock_search,
            patch('services.ldmf_country.LDMFCountryService.list', new_callable=AsyncMock) as mock_list,
            patch('services.client_industry.ClientIndustryDataService.list') as mock_industry_list,
            patch('services.client_service.ClientServiceDataService.list') as mock_service_list,
            patch('services.engagement_location.EngagementLocationDataService.list') as mock_engagement_locations_list,
        ):
            # Set up mocks
            mock_conversation = type(
                'MockConversation',
                (),
                {'id': test_conversation_id, 'State': 'collecting_client_name', 'StateContext': {}},
            )
            mock_get_conversation.return_value = mock_conversation
            mock_get_confirmed_data.return_value = ConfirmedData()

            # Mock multiple client search results
            mock_search.return_value = ClientSearchResponse(
                clients=[
                    ClientSearchItem(id=1, name='Apple Inc.', qualsCount=30, clientConfidentiality=1),
                    ClientSearchItem(id=2, name='Apple Corp', qualsCount=15, clientConfidentiality=1),
                ],
                total_count=2,
                page_size=5,
                page_idx=0,
                exact_match=False,
            )

            # Mock LDMF country service
            mock_list.return_value = {}

            # Mock industry list to return empty list
            mock_industry_list.return_value = []

            # Mock service list to return empty list
            mock_service_list.return_value = []

            # Mock engagement locations list to return empty list
            mock_engagement_locations_list.return_value = []

            message_url = url_resolver.reverse(operation_ids.message.CREATE)
            message_data = {
                'conversation_id': str(test_conversation_id),
                'role': MessageRole.USER,
                'message_type': MessageType.TEXT,
                'content': user_message,
            }

            message_response = await async_client.post(message_url, headers=auth_header, data=message_data)

        # Verify response
        assert message_response.status_code == status.HTTP_201_CREATED
        data = message_response.json()

        # Should not generate a system message
        assert 'system' in data
        assert data['system'] is None

    async def test_no_client_match_requires_confirmation(
        self,
        auth_header,
        async_client: CustomAsyncClient,
        url_resolver,
        test_conversation_id,
    ):
        """
        Test that when no matches are found, user confirmation is still required.
        """
        user_message = 'I need help with my qual for Unknown Company'

        with (
            patch(
                'services.message_processor.ConversationMessageProcessor._get_intent',
                new_callable=AsyncMock,
                return_value=ConversationMessageIntention.EXTRACTION,
            ),
            patch('repositories.quals_clients.QualsClientsRepository.search_clients') as mock_search,
            patch('services.ldmf_country.LDMFCountryService.list', new_callable=AsyncMock) as mock_list,
            patch('services.client_industry.ClientIndustryDataService.list') as mock_industry_list,
            patch('services.client_service.ClientServiceDataService.list') as mock_service_list,
            patch('services.engagement_location.EngagementLocationDataService.list') as mock_engagement_locations_list,
        ):
            # Mock no client search results
            mock_search.return_value = ClientSearchResponse(
                clients=[],
                total_count=0,
                page_size=5,
                page_idx=0,
                exact_match=False,
            )

            # Mock LDMF country service
            mock_list.return_value = {}

            # Mock industry list to return empty list
            mock_industry_list.return_value = []

            # Mock service list to return empty list
            mock_service_list.return_value = []

            # Mock engagement locations list to return empty list
            mock_engagement_locations_list.return_value = []

            message_url = url_resolver.reverse(operation_ids.message.CREATE)
            message_data = {
                'conversation_id': str(test_conversation_id),
                'role': MessageRole.USER,
                'message_type': MessageType.TEXT,
                'content': user_message,
            }

            message_response = await async_client.post(message_url, headers=auth_header, data=message_data)

        assert message_response.status_code == status.HTTP_201_CREATED
        data = message_response.json()
        # Now it should not generate a system message
        assert 'system' in data
        assert data['system'] is None


class TestClientNameDetectionPatterns:
    """Test client name detection patterns."""

    async def test_user_message_is_none(self, test_conversation_id):
        mock_conversation = type(
            'MockConversation', (), {'id': test_conversation_id, 'State': 'collecting_client_name', 'StateContext': {}}
        )
        mock_confirmed_data = AsyncMock()
        mock_aggregated_data = AsyncMock()
        mock_conversation_data = AsyncMock()
        mock_conversation_data.conversation = mock_conversation
        mock_conversation_data.confirmed_data = mock_confirmed_data
        mock_conversation_data.extracted_data = mock_aggregated_data

        with pytest.raises(ValidationError):
            user_serial = UserMessageSerializer(
                conversation_id=test_conversation_id,
                role=MessageRole.USER,
                type=MessageType.TEXT,
                content=None,  # type: ignore
                created_at=datetime.now(),
                id=UUID('00000000-0000-0000-0000-000000000001'),
                selected_option=None,
            )
            # Create a mock processor to test the detection method
            processor = ConversationMessageProcessor(
                conversation_id=test_conversation_id,
                conversation_data=mock_conversation_data,
                user_message=user_serial,  # type: ignore
                files=None,
                intent_classifier_service=AsyncMock(),
                extracted_data_service=AsyncMock(),
                conversation_repository=AsyncMock(),
                date_validator_service=AsyncMock(),
                document_service=AsyncMock(),
            )
            _ = await processor.run()
