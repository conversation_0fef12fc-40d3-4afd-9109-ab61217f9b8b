from uuid import uuid4

import pytest

from schemas import AggregatedData, CombinedExtractedDataResponse, ConfirmedData, TeamMember


@pytest.mark.asyncio
async def test_team_roles_aggregation_with_mocked_team_members():
    confirmed_data = ConfirmedData(
        objective_and_scope='Scope', outcomes='Outcome', date_intervals=('2025-01-01', '2025-01-31')
    )
    aggregated_data = AggregatedData(
        team_roles=[
            {'name': '<PERSON>', 'email': '<EMAIL>', 'is_approver': True},
            {'name': '<PERSON>', 'email': '<EMAIL>', 'is_approver': False},
        ]
    )
    conversation_id = uuid4()
    response = await CombinedExtractedDataResponse.from_confirmed_and_aggregated_data(
        confirmed_data=confirmed_data, aggregated_data=aggregated_data, conversation_id=conversation_id
    )
    assert isinstance(response.team_roles, list)
    assert all(isinstance(member, TeamMember) for member in response.team_roles)
    assert response.team_roles[0].name == 'Alice'
    assert response.team_roles[1].email == '<EMAIL>'


@pytest.mark.asyncio
async def test_roles_and_services_aggregation_with_mocked_data():
    confirmed_data = ConfirmedData(
        objective_and_scope='Scope', outcomes='Outcome', date_intervals=('2025-01-01', '2025-01-31')
    )
    aggregated_data = AggregatedData(team_roles=[{'name': 'Charlie', 'email': '<EMAIL>'}])

    conversation_id = uuid4()
    response = await CombinedExtractedDataResponse.from_confirmed_and_aggregated_data(
        confirmed_data=confirmed_data, aggregated_data=aggregated_data, conversation_id=conversation_id
    )
    assert response.team_roles[0].name == 'Charlie'
    assert response.team_roles[0].email == '<EMAIL>'
