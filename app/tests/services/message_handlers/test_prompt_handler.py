from unittest.mock import AsyncMock, MagicMock, patch
import uuid

import pytest

from constants.message import (
    CorruptedUtilsTemplate,
    SuggestedUserPrompt,
    SystemReplyType,
)
from services.message_handlers.prompt_handler import Prompt<PERSON>essageHandler


async def test_build_corrupted_file_error_message_single_file_corrupted():
    # Arrange
    last_message = MagicMock()
    last_message.id = uuid.uuid4()
    last_message.conversation_id = uuid.uuid4()

    document_db_repository = MagicMock()
    document_db_repository.get_total_document_count_for_message = AsyncMock(return_value=1)

    processing_message_repository = MagicMock()
    processing_message_repository.get_corrupted_filenames_for_message = AsyncMock(return_value=['test.pdf'])

    handler = PromptMessageHandler(
        conversation_message_repository=MagicMock(),
        conversation_repository=MagicMock(),
        document_service=MagicMock(),
        document_db_repository=document_db_repository,
        processing_message_repository=processing_message_repository,
        kx_dash_service=MagicMock(),
        translation_service=MagicMock(),
        extracted_data_service=MagicMock(),
        date_validator_service=MagicMock(),
        system_message_generation_service=MagicMock(),
        client_industry_service=MagicMock(),
        client_service_service=MagicMock(),
        engagement_location_service=MagicMock(),
        openai_repository=MagicMock(),
        client_name_option_handler=MagicMock(),
        ldmf_country_option_handler=MagicMock(),
        dates_option_handler=MagicMock(),
        kx_dash_task_option_handler=MagicMock(),
        conflict_option_handler=MagicMock(),
        conflict_service=MagicMock(),
        openai_client_name_validation_service=MagicMock(),
    )

    # Act
    with patch('config.settings.support_url', 'http://support.com'):
        message_data = await handler._build_corrupted_file_error_message(last_message)

    # Assert
    assert message_data.system_reply_type == SystemReplyType.CORRUPTED_FILE
    assert message_data.suggested_prompts == []
    assert '<b>test.pdf</b>' in message_data.content
    assert 'http://support.com' in message_data.content


@pytest.mark.parametrize(
    'total_docs, corrupted_filenames, expected_utils_part, expected_prompts',
    [
        (
            2,
            ['test1.pdf'],
            CorruptedUtilsTemplate.ONE_OF_UPLOADED,
            [SuggestedUserPrompt.CONTINUE_WITHOUT_ERROR_FILE.value],
        ),
        (
            3,
            ['test1.pdf'],
            CorruptedUtilsTemplate.ONE_OF_UPLOADED,
            [SuggestedUserPrompt.CONTINUE_WITHOUT_ERROR_FILE.value],
        ),
        (2, ['test1.pdf', 'test2.pdf'], CorruptedUtilsTemplate.ALL, []),
        (
            3,
            ['test1.pdf', 'test2.pdf'],
            CorruptedUtilsTemplate.TWO_OF_UPLOADED,
            [SuggestedUserPrompt.CONTINUE_WITHOUT_ERROR_FILE.value],
        ),
        (3, ['test1.pdf', 'test2.pdf', 'test3.pdf'], CorruptedUtilsTemplate.ALL, []),
        (4, ['test1.pdf', 'test2.pdf', 'test3.pdf', 'test4.pdf'], CorruptedUtilsTemplate.ALL, []),
    ],
)
async def test_build_corrupted_file_error_message_multiple_files(
    total_docs, corrupted_filenames, expected_utils_part, expected_prompts
):
    # Arrange
    last_message = MagicMock()
    last_message.id = uuid.uuid4()
    last_message.conversation_id = uuid.uuid4()

    document_db_repository = MagicMock()
    document_db_repository.get_total_document_count_for_message = AsyncMock(return_value=total_docs)

    processing_message_repository = MagicMock()
    processing_message_repository.get_corrupted_filenames_for_message = AsyncMock(return_value=corrupted_filenames)

    handler = PromptMessageHandler(
        conversation_message_repository=MagicMock(),
        conversation_repository=MagicMock(),
        document_service=MagicMock(),
        document_db_repository=document_db_repository,
        processing_message_repository=processing_message_repository,
        kx_dash_service=MagicMock(),
        translation_service=MagicMock(),
        extracted_data_service=MagicMock(),
        date_validator_service=MagicMock(),
        system_message_generation_service=MagicMock(),
        client_industry_service=MagicMock(),
        client_service_service=MagicMock(),
        engagement_location_service=MagicMock(),
        openai_repository=MagicMock(),
        client_name_option_handler=MagicMock(),
        ldmf_country_option_handler=MagicMock(),
        dates_option_handler=MagicMock(),
        kx_dash_task_option_handler=MagicMock(),
        conflict_option_handler=MagicMock(),
        conflict_service=MagicMock(),
        openai_client_name_validation_service=MagicMock(),
    )

    # Act
    with patch('config.settings.support_url', 'http://support.com'):
        message_data = await handler._build_corrupted_file_error_message(last_message)

    # Assert
    assert message_data.system_reply_type == SystemReplyType.CORRUPTED_FILE
    assert message_data.suggested_prompts == expected_prompts
    assert str(expected_utils_part) in message_data.content
    for filename in corrupted_filenames:
        assert f'<b>{filename}</b>' in message_data.content
    assert 'http://support.com' in message_data.content
