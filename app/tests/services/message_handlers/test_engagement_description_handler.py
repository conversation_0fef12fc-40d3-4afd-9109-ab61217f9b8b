from datetime import datetime
from unittest.mock import AsyncMock, MagicMock
import uuid

import pytest

from constants.engagement import EngagementMessageIntention
from constants.message import MessageR<PERSON>, MessageType, SystemReplyType
from schemas import (
    CombinedMessageSerializer,
    EngagementFieldModificationResponse,
    SystemMessageSerializer,
    UserMessageSerializer,
)
from schemas.engagement_message import EngagementMessageIntentClassifierServiceResponse
from services.message_handlers.engagement_description_handler import (
    EngagementDescriptionMessageHandler,
)


@pytest.mark.asyncio
async def test_handle_field_modification_intent_returns_updated_qual_fields():
    conversation_id = uuid.uuid4()
    user_request = 'update business issues'
    field_name = 'business_issues'
    updated_content = 'new business issues'

    mock_convo_message_repo = AsyncMock()
    mock_openai_repo = AsyncMock()
    mock_engagement_intent_processor = MagicMock()
    mock_engagement_intent_processor.process_intent.return_value.response = 'Field updated successfully.'
    mock_engagement_intent_processor.process_intent_reply_type.return_value = (
        SystemReplyType.ENGAGEMENT_DETAILS_FIELD_MODIFIED
    )
    mock_engagement_field_modification_service = AsyncMock()
    mock_command_service = AsyncMock()

    handler = EngagementDescriptionMessageHandler(
        conversation_message_repository=mock_convo_message_repo,
        openai_repository=mock_openai_repo,
        engagement_intent_processor=mock_engagement_intent_processor,
        engagement_field_modification_service=mock_engagement_field_modification_service,
        command_service=mock_command_service,
        field_repository=AsyncMock(),
        conversation_repository=AsyncMock(),
        extracted_data_service=AsyncMock(),
    )

    # Mock field modification response
    mock_engagement_field_modification_service.modify_field.return_value = EngagementFieldModificationResponse(
        success=True,
        field_name=field_name,
        updated_content=updated_content,
        original_content='old business issues',
        error=None,
    )

    # Mock OpenAI intent classification response
    mock_openai_repo.generate_chat_completion.return_value = EngagementMessageIntentClassifierServiceResponse(
        intention=EngagementMessageIntention.BUSINESS_ISSUES
    )

    mock_convo_message_repo.create.side_effect = [
        UserMessageSerializer(
            id=uuid.uuid4(),
            conversation_id=conversation_id,
            role=MessageRole.USER,
            type=MessageType.TEXT,
            content=user_request,
            created_at=datetime.now(),
            selected_option=None,
        ),
        SystemMessageSerializer(
            id=uuid.uuid4(),
            conversation_id=conversation_id,
            role=MessageRole.SYSTEM,
            type=MessageType.TEXT,
            content='Field updated successfully.',
            created_at=datetime.now(),
            options=[],
            qual_fields={field_name: {'context': updated_content, 'snippet': updated_content}},  # type: ignore
            system_reply_type=SystemReplyType.ENGAGEMENT_DETAILS_FIELD_MODIFIED,
        ),
    ]

    # Act
    result = await handler.handle(
        conversation_id=conversation_id,
        content=user_request,
        selected_option=None,
        command=None,
        files=None,
        token='test_token',
    )

    # Assert: qual_fields should contain the updated value
    assert isinstance(result, CombinedMessageSerializer)
    assert result.system is not None
    assert result.system.qual_fields is not None
    assert hasattr(result.system.qual_fields, field_name)
    field_obj = getattr(result.system.qual_fields, field_name)
    assert field_obj.context == updated_content
    assert field_obj.snippet == updated_content
