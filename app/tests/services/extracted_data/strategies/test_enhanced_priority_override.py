from datetime import datetime, timezone
from uuid import uuid4

import pytest

from constants.extracted_data import DataSourceType
from schemas import ExtractedData
from services.extracted_data.strategies.enhanced_priority_override import EnhancedPriorityOverrideStrategy


@pytest.fixture
def strategy():
    """Fixture to create an instance of the EnhancedPriorityOverrideStrategy."""
    return EnhancedPriorityOverrideStrategy()


def create_test_extracted_data(**kwargs):
    """Helper function to create ExtractedData instances with default values."""
    defaults = {
        'ConversationPublicId': uuid4(),
        'DataSourceType': DataSourceType.PROMPT,
        'CreatedAt': datetime.now(timezone.utc),
    }
    defaults.update(kwargs)
    return ExtractedData.model_validate(defaults)


def test_process_confidential_override(strategy):
    """Test that confidential status is correctly overridden."""
    # Initial data with unknown fee
    initial_data = create_test_extracted_data(
        IsEngagementFeeUnknown=True,
        EngagementFeeDisplay='Unknown',
    )
    strategy.process(initial_data)

    # New data with confidential fee
    confidential_data = create_test_extracted_data(
        EngagementFeeDisplay='Confidential (do not display)',
        IsEngagementFeeUnknown=False,
    )
    strategy.process(confidential_data)

    assert strategy.get_engagement_fee_display() == 'Confidential (do not display)'
    assert not strategy.get_is_engagement_fee_unknown()


def test_process_pro_bono_override(strategy):
    """Test that pro bono status is correctly overridden."""
    # Initial data with a known fee
    initial_data = create_test_extracted_data(
        EngagementFee=10000,
        EngagementFeeCurrency='USD',
        IsEngagementFeeUnknown=False,
    )
    strategy.process(initial_data)

    # New data with pro bono status
    pro_bono_data = create_test_extracted_data(
        IsProBono=True,
    )
    strategy.process(pro_bono_data)

    assert strategy.get_is_pro_bono()
    assert not strategy.get_is_engagement_fee_unknown()
    assert strategy.get_engagement_fee_display() is None


def test_process_no_override_for_unknown(strategy):
    """Test that unknown status does not override a known status."""
    # Initial data with confidential fee
    initial_data = create_test_extracted_data(
        EngagementFeeDisplay='Confidential (do not display)',
        IsEngagementFeeUnknown=False,
    )
    strategy.process(initial_data)

    # New data with unknown fee
    unknown_data = create_test_extracted_data(
        IsEngagementFeeUnknown=True,
        EngagementFeeDisplay='Unknown',
    )
    strategy.process(unknown_data)

    assert strategy.get_engagement_fee_display() == 'Confidential (do not display)'
    assert not strategy.get_is_engagement_fee_unknown()


def test_source_has_fee_data_confidential(strategy):
    """Test that _source_has_fee_data correctly identifies confidential data."""
    data = create_test_extracted_data(EngagementFeeDisplay='Confidential (do not display)')
    assert strategy._source_has_fee_data(data)


def test_source_has_fee_data_pro_bono(strategy):
    """Test that _source_has_fee_data correctly identifies pro bono data."""
    data = create_test_extracted_data(IsProBono=True)
    assert strategy._source_has_fee_data(data)


def test_source_has_fee_data_other_fields(strategy):
    """Test that _source_has_fee_data correctly identifies other fee-related data."""
    data = create_test_extracted_data(EngagementFee=5000)
    assert strategy._source_has_fee_data(data)


def test_source_has_no_fee_data(strategy):
    """Test that _source_has_fee_data correctly identifies when no fee data is present."""
    data = create_test_extracted_data()
    assert not strategy._source_has_fee_data(data)
