from fastapi import status
import pytest

from constants.engagement import engagement_templates
from constants.extracted_data import ConversationState, DataSourceType
from constants.operation_ids import operation_ids
from core.http_client import CustomAsyncClient
from core.urls import URLResolver
from schemas.confirmed_data import ConfirmedData


@pytest.mark.parametrize('page_type', ['engagement_details', 'usage_and_team_details', 'engagement_description'])
async def test_start_new_message_chat_with_page_type(
    auth_mock,
    auth_header,
    async_client: CustomAsyncClient,
    conversation_repository_dep_with_autocommit,
    extracted_data_repository_real_with_autocommit,
    url_resolver: URLResolver,
    test_conversation_id,
    page_type,
):
    """Test starting an engagement descriptions chat with a specific page type."""
    confirmed_data = ConfirmedData(
        client_name='Multi Test Client',
        ldmf_country='Canada',
        date_intervals=('2023-03-01', '2024-02-29'),
        objective_and_scope='Test enhanced extraction for qual usage',
        outcomes='Successfully tested all qual usage values',
        proposed_client_name='Multi Test Client',
    )
    state = ConversationState.DATA_COMPLETE
    await conversation_repository_dep_with_autocommit.update_confirmed_data_and_state(
        test_conversation_id,
        confirmed_data=confirmed_data,
        state=state,
    )

    await extracted_data_repository_real_with_autocommit.create(test_conversation_id, DataSourceType.DOCUMENTS)
    start_chat_url = url_resolver.reverse(
        operation_ids.conversation.ENGAGEMENT_CHAT_CREATE, conversation_id=test_conversation_id
    )
    # welcome messages
    response = await async_client.post(start_chat_url, headers=auth_header)

    assert response.status_code == status.HTTP_200_OK

    # post new message as user to different types
    message_url = url_resolver.reverse(operation_ids.message.CREATE)
    form_data = {
        'conversation_id': str(test_conversation_id),
        'content': 'Test message content',
        'page_type': page_type,
    }
    response = await async_client.post(message_url, headers=auth_header, data=form_data)
    assert response.status_code == status.HTTP_201_CREATED
    data = response.json()
    assert data['user']['content'] == 'Test message content'
    assert data['user']['page_type'] == page_type
    if page_type == 'engagement_description':
        assert data['system']['content'] == engagement_templates.general.undefined_reply
    else:
        assert data['system']['content'] == engagement_templates.general.sorry_cant_help_reply
