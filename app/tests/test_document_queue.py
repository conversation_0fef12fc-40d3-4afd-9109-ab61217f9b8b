import json
from uuid import uuid4

from pydantic import HttpUrl

from config import settings
from repositories import DocumentBlobStorageRepository, DocumentQueueRepository
from schemas import QualQueueMessage, Source


test_queue_name = 'document-processing-test'


async def test_document_queue_initialise():
    queue_repo = DocumentQueueRepository(settings.document_queue.connection_string, test_queue_name)
    assert queue_repo.queue_client is not None


async def test_send_unified_message_with_documents():
    """Test sending a unified message with document URLs."""
    blob_repo = DocumentBlobStorageRepository(
        settings.blob_storage.connection_string, settings.blob_storage.document_container_name
    )
    upload_url1 = await blob_repo.upload(
        file_name='test1.txt', content=b'Test file content 1', content_type='text/plain'
    )
    upload_url2 = await blob_repo.upload(
        file_name='test2.txt', content=b'Test file content 2', content_type='text/plain'
    )

    queue_repo = DocumentQueueRepository(settings.document_queue.connection_string, test_queue_name)
    signalr_user_id = uuid4()

    message = QualQueueMessage(
        source=Source(documents=[HttpUrl(upload_url1), HttpUrl(upload_url2)]),
        signal_r_connection_id=str(signalr_user_id),
    )

    queue_message = await queue_repo.send_message(message)
    assert queue_message is not None
    assert queue_message.id is not None
    assert queue_message.inserted_on is not None
    assert queue_message.expires_on is not None
    assert queue_message.pop_receipt is not None
    assert queue_message.content is not None

    # Verify message content
    message_content = json.loads(queue_message.content.decode('utf-8'))
    assert message_content['source']['documents'] == [upload_url1, upload_url2]
    assert message_content['source']['text_prompt'] is None
    assert message_content['signal_r_connection_id'] == str(signalr_user_id)


async def test_send_unified_message_with_text_prompt():
    """Test sending a unified message with text prompt URL."""
    blob_repo = DocumentBlobStorageRepository(
        settings.blob_storage.connection_string, settings.blob_storage.document_container_name
    )
    prompt_url = await blob_repo.upload(
        file_name='prompt.txt', content=b'Test prompt content', content_type='text/plain'
    )

    queue_repo = DocumentQueueRepository(settings.document_queue.connection_string, test_queue_name)
    signalr_user_id = uuid4()

    message = QualQueueMessage(
        source=Source(text_prompt=HttpUrl(prompt_url)), signal_r_connection_id=str(signalr_user_id)
    )

    queue_message = await queue_repo.send_message(message)
    assert queue_message is not None
    assert queue_message.id is not None

    # Verify message content
    message_content = json.loads(queue_message.content.decode('utf-8'))
    assert message_content['source']['text_prompt'] == prompt_url
    assert message_content['source']['documents'] is None
    assert message_content['signal_r_connection_id'] == str(signalr_user_id)


async def test_send_unified_message_with_both_text_and_documents():
    """Test sending a unified message with both text prompt and document URLs."""
    blob_repo = DocumentBlobStorageRepository(
        settings.blob_storage.connection_string, settings.blob_storage.document_container_name
    )
    prompt_url = await blob_repo.upload(
        file_name='prompt.txt', content=b'Test prompt content', content_type='text/plain'
    )
    document_url = await blob_repo.upload(
        file_name='document.txt', content=b'Test document content', content_type='text/plain'
    )

    queue_repo = DocumentQueueRepository(settings.document_queue.connection_string, test_queue_name)
    signalr_user_id = uuid4()

    message = QualQueueMessage(
        source=Source(text_prompt=HttpUrl(prompt_url), documents=[HttpUrl(document_url)]),
        signal_r_connection_id=str(signalr_user_id),
    )

    queue_message = await queue_repo.send_message(message)
    assert queue_message is not None
    assert queue_message.id is not None

    # Verify message content
    message_content = json.loads(queue_message.content.decode('utf-8'))
    assert message_content['source']['text_prompt'] == prompt_url
    assert message_content['source']['documents'] == [document_url]
    assert message_content['signal_r_connection_id'] == str(signalr_user_id)
