import json
from unittest.mock import AsyncMock, patch
from uuid import uuid4

from fastapi import status
import pytest

from constants.engagement import EngagementMessageIntention, engagement_templates
from constants.message import (
    MessageRole,
    MessageType,
    PageType,
    QualFieldName,
    SystemReplyType,
    TextEditCommand,
)
from constants.operation_ids import operation_ids
from core.http_client import CustomAsyncClient
from core.urls import URL<PERSON><PERSON>olver
from models.qual_field_value import Qual<PERSON><PERSON>Value
from schemas import Command, ConfirmedData, MessageValidator, SumaryResponse, TextEditCommandValidator
from services.command import CommandService
from services.text_edit import TextEditService


class TestCommandAPIIntegration:
    """Integration tests for command functionality through the API."""

    @pytest.mark.parametrize(
        'command_type,field_name,expected_system_reply_type',
        [
            (
                TextEditCommand.EXPAND,
                QualFieldName.BUSINESS_ISSUES,
                SystemReplyType.FIELD_SAVED,
            ),
            (
                TextEditCommand.REWRITE,
                QualFieldName.SCOPE_APPROACH,
                SystemReplyType.FIELD_SAVED,
            ),
            (
                TextEditCommand.SHORTEN,
                QualFieldName.VALUE_DELIVERED,
                SystemReplyType.FIELD_SAVED,
            ),
            (
                TextEditCommand.PROMPT,
                QualFieldName.BUSINESS_ISSUES,
                SystemReplyType.FIELD_SAVED,
            ),
        ],
    )
    async def test_command_processing_through_api(
        self,
        command_type: TextEditCommand,
        field_name: QualFieldName,
        expected_system_reply_type: SystemReplyType,
        auth_mock,
        auth_header,
        async_client: CustomAsyncClient,
        url_resolver: URLResolver,
        test_conversation_id,
    ):
        """Test command processing through the message creation API."""
        # Prepare command data
        original_context = "This is the original business context that needs editing. It contains multiple sentences with detailed information about the client's current situation and challenges."
        snippet = 'business context that needs editing'
        command_data = {
            'command': command_type.value,
            'field_name': field_name.value,
            'context': original_context,
            'formatted_context': original_context,
            'snippet': snippet,
        }

        # Mock the TextEditService response
        expected_edited_snippet = 'EXPANDED business context that needs editing'

        # Calculate what the reconstructed context should be
        snippet_index = original_context.find(snippet)
        text_before_snippet = original_context[:snippet_index]
        text_after_snippet = original_context[snippet_index + len(snippet) :]

        with patch('services.text_edit.TextEditService.text_edit', new_callable=AsyncMock) as mock_text_edit:
            mock_text_edit.return_value = expected_edited_snippet

            # Create message with command
            message_url = url_resolver.reverse(operation_ids.message.CREATE)
            form_data = {
                'conversation_id': str(test_conversation_id),
                'content': '',
                'command': json.dumps(command_data),
                'page_type': PageType.ENGAGEMENT_DESCRIPTION.value,
            }

            response = await async_client.post(message_url, headers=auth_header, data=form_data)

            # Verify response
            assert response.status_code == status.HTTP_201_CREATED
            data = response.json()

            # Check user message structure
            assert data['user']['conversation_id'] == str(test_conversation_id)
            assert data['user']['role'] == str(MessageRole.USER)
            assert data['user']['type'] == str(MessageType.TEXT)
            assert data['user']['page_type'] == PageType.ENGAGEMENT_DESCRIPTION.value

            # Check system message structure
            assert data['system']['role'] == str(MessageRole.SYSTEM)
            assert data['system']['type'] == str(MessageType.TEXT)
            assert data['system']['system_reply_type'] == str(expected_system_reply_type)
            assert 'is_undoable' in data['system']

            # Check command in user message (where it's actually stored)
            returned_command = data['user']['command']
            assert returned_command['command'] == command_type.value
            assert returned_command['field_name'] == field_name.value
            assert returned_command['context'] == original_context  # Original context, not processed
            assert returned_command['snippet'] == snippet  # Original snippet, not processed

            # Verify TextEditService was called correctly
            expected_data_to_analyze = {
                'full_text': original_context,
                'snippet': snippet,
                'text_before_snippet': text_before_snippet,
                'text_after_snippet': text_after_snippet,
                'result_length': None,
            }

            # For PROMPT commands, user_request should be included
            if command_type == TextEditCommand.PROMPT:
                expected_data_to_analyze['user_request'] = ''

            mock_text_edit.assert_called_once_with(
                command=command_type,
                data_to_analyze=expected_data_to_analyze,
                temperature=0.0,  # Default temperature from settings
                model='gpt-4o',  # Default model from settings
            )

    async def test_malformed_command_json_format(
        self,
        auth_mock,
        auth_header,
        async_client: CustomAsyncClient,
        url_resolver: URLResolver,
        test_conversation_id,
    ):
        """Test API response when command JSON is malformed."""
        message_url = url_resolver.reverse(operation_ids.message.CREATE)
        form_data = {
            'conversation_id': str(test_conversation_id),
            'content': '',
            'command': '{"invalid": "json"',  # Malformed JSON
            'page_type': PageType.ENGAGEMENT_DESCRIPTION.value,
        }

        response = await async_client.post(message_url, headers=auth_header, data=form_data)

        assert response.status_code == status.HTTP_400_BAD_REQUEST
        error_data = response.json()
        assert 'command' in str(error_data).lower()

    async def test_missing_required_command_fields(
        self,
        auth_mock,
        auth_header,
        async_client: CustomAsyncClient,
        url_resolver: URLResolver,
        test_conversation_id,
    ):
        """Test API response when command is missing required fields."""
        # Command missing 'snippet' field
        incomplete_command = {
            'command': TextEditCommand.EXPAND.value,
            'field_name': QualFieldName.BUSINESS_ISSUES.value,
            'context': 'Some context text',
            # Missing 'snippet' field
        }

        message_url = url_resolver.reverse(operation_ids.message.CREATE)
        form_data = {
            'conversation_id': str(test_conversation_id),
            'content': '',
            'command': json.dumps(incomplete_command),
            'page_type': PageType.ENGAGEMENT_DESCRIPTION.value,
        }

        response = await async_client.post(message_url, headers=auth_header, data=form_data)

        assert response.status_code == status.HTTP_400_BAD_REQUEST
        error_data = response.json()
        assert 'snippet' in str(error_data).lower()


class TestCommandService:
    """Unit tests for the CommandService class."""

    @pytest.fixture
    def command_service(self):
        """Create a CommandService instance with mocked dependencies."""
        mock_field_value_repository = AsyncMock()
        mock_text_edit_service = AsyncMock()
        mock_openai_repository = AsyncMock()
        mock_conversation_repository = AsyncMock()
        mock_extracted_data_service = AsyncMock()
        return CommandService(
            field_repository=mock_field_value_repository,
            text_edit_service=mock_text_edit_service,
            openai_repository=mock_openai_repository,
            conversation_repository=mock_conversation_repository,
            extracted_data_service=mock_extracted_data_service,
        )

    @pytest.fixture
    def sample_command(self):
        """Create a sample command for testing."""
        return Command(
            command=TextEditCommand.EXPAND,
            field_name=QualFieldName.BUSINESS_ISSUES,
            context='Original business issues text that needs to be expanded.',
            formatted_context='Original business issues text that needs to be expanded.',
            snippet='business issues text',
        )

    async def test_command_service_handle_success(self, command_service, sample_command):
        """Test successful command handling by CommandService."""
        conversation_id = uuid4()
        edited_text = 'EXPANDED business issues text'
        generated_summary = 'Generated summary'

        # Patch dependencies
        command_service.text_edit_service.text_edit = AsyncMock(return_value=edited_text)

        # Patch dependencies
        command_service.text_edit_service.text_edit = AsyncMock(return_value=edited_text)
        command_service.openai_repository.generate_chat_completion = AsyncMock(
            return_value=SumaryResponse(value=generated_summary)
        )
        command_service.field_repository.create = AsyncMock()

        # Call the service
        result = await command_service.process_command(sample_command, 'User content', conversation_id)

        # Verify repository was called for original and regenerated fields
        command_service.field_repository.create.assert_any_call(
            conversation_id=conversation_id,
            field_name=sample_command.field_name,
            field_value='Original business issues text that needs to be expanded.',
            formatted_field_value='Original business issues text that needs to be expanded.',
        )

        # Verify text edit service was called
        command_service.text_edit_service.text_edit.assert_awaited_once()

        # Verify the result
        assert isinstance(result, MessageValidator)
        assert result.conversation_id == conversation_id
        assert result.role == MessageRole.SYSTEM
        assert result.type == MessageType.TEXT
        assert result.system_reply_type == SystemReplyType.FIELD_SAVED
        assert result.is_undoable is True
        # The result command should have the reconstructed context, not just the edited text
        expected_reconstructed_result = 'Original ' + edited_text + ' that needs to be expanded.'
        if result.command:
            assert result.command.context == expected_reconstructed_result
            assert result.command.command == sample_command.command
            assert result.command.field_name == sample_command.field_name
            assert result.command.snippet == edited_text

    async def test_command_service_preserves_original_command_properties(self, command_service, sample_command):
        conversation_id = uuid4()
        edited_text = 'New edited text'
        command_service.text_edit_service.text_edit.return_value = edited_text
        result = await command_service.process_command(sample_command, 'User content', conversation_id)
        # Command context should be reconstructed with text_before_snippet + edited_text + text_after_snippet
        # Since the snippet "business issues text" appears in "Original business issues text that needs to be expanded."
        # text_before_snippet = "Original ", text_after_snippet = " that needs to be expanded."
        expected_reconstructed_context = 'Original ' + edited_text + ' that needs to be expanded.'
        assert sample_command.context == expected_reconstructed_context
        # Result command should have updated context (reconstructed with before + edited + after)
        expected_reconstructed_result = 'Original ' + edited_text + ' that needs to be expanded.'
        if result.command:
            assert result.command.context == expected_reconstructed_result
            assert result.command.command == sample_command.command
            assert result.command.field_name == sample_command.field_name
            assert result.command.snippet == edited_text

    @pytest.mark.parametrize(
        'command_type,is_undoable',
        [
            (TextEditCommand.EXPAND, True),
            (TextEditCommand.REWRITE, True),
            (TextEditCommand.SHORTEN, True),
            (TextEditCommand.PROMPT, True),
            (TextEditCommand.STORE, False),
        ],
    )
    async def test_process_command_sets_is_undoable_flag(
        self, command_service: CommandService, command_type: TextEditCommand, is_undoable: bool
    ):
        """Test that process_command sets the is_undoable flag correctly based on the command type."""
        # Arrange
        conversation_id = uuid4()
        command = Command(
            command=command_type,
            field_name=QualFieldName.BUSINESS_ISSUES,
            context='Some context',
            formatted_context='Some context',
            snippet='Some snippet',
        )
        # Mock dependencies
        command_service.text_edit_service.text_edit = AsyncMock(return_value='Edited text')
        command_service.openai_repository.generate_chat_completion = AsyncMock(
            return_value=SumaryResponse(value='Generated summary')
        )
        command_service.field_repository.create = AsyncMock()
        # Act
        result = await command_service.process_command(command, 'User content', conversation_id)
        # Assert
        assert result.is_undoable is is_undoable

    async def test_undo_command_sets_is_undoable_to_false(self, command_service: CommandService):
        """Test that the undo_command sets the is_undoable flag to False in its response."""
        # Arrange
        conversation_id = uuid4()
        command = Command(
            command=TextEditCommand.UNDO,
            field_name=QualFieldName.BUSINESS_ISSUES,
            context='Some context',
            formatted_context='Some context',
            snippet='Some snippet',
        )
        # Mock the repository to return a previous QualFieldValue or None

        previous_value = QualFieldValue(
            QualConversationId=conversation_id,
            FieldName=command.field_name,
            FieldValue='Previous value',
            FormattedFieldValue='Previous value',
        )
        command_service.field_repository.get_previous_value = AsyncMock(return_value=previous_value)
        mock_extracted_data = AsyncMock()
        mock_extracted_data.business_issues = 'original business issues'
        mock_extracted_data.engagement_summary = 'original summary'
        mock_extracted_data.one_line_description = 'original one line'
        command_service.extracted_data_service.aggregate_data = AsyncMock(return_value=mock_extracted_data)
        # Act

        result = await command_service.process_command(command, 'User content', conversation_id)
        # Assert
        assert result.is_undoable is False

    async def test_undo_field_change_no_previous_value(self, command_service: CommandService):
        """Test that undoing a field with no previous value resets it to empty."""
        # Arrange
        conversation_id = uuid4()
        command = Command(
            command=TextEditCommand.UNDO,
            field_name=QualFieldName.BUSINESS_ISSUES,
            context='Current value',
            formatted_context='Current value',
            snippet='',
        )

        # Mock the repository to return None for the previous value
        command_service.field_repository.get_previous_value = AsyncMock(return_value=None)
        command_service.field_repository.create = AsyncMock()
        mock_extracted_data = AsyncMock()
        mock_extracted_data.business_issues = 'Business issues extracted from document'
        mock_extracted_data.engagement_summary = 'Summary extracted from document'
        mock_extracted_data.one_line_description = 'One line extracted from document'
        command_service.extracted_data_service.aggregate_data = AsyncMock(return_value=mock_extracted_data)

        # Act
        result = await command_service.process_command(command, '', conversation_id)

        # Assert
        # Verify that the fields are created with an empty value
        assert command_service.field_repository.create.call_count == 3
        command_service.field_repository.create.assert_any_call(
            conversation_id=conversation_id,
            field_name=QualFieldName.ONE_LINE_DESCRIPTION,
            field_value=mock_extracted_data.one_line_description,
            formatted_field_value=mock_extracted_data.one_line_description,
        )
        command_service.field_repository.create.assert_any_call(
            conversation_id=conversation_id,
            field_name=QualFieldName.ENGAGEMENT_SUMMARY,
            field_value=mock_extracted_data.engagement_summary,
            formatted_field_value=mock_extracted_data.engagement_summary,
        )
        command_service.field_repository.create.assert_any_call(
            conversation_id=conversation_id,
            field_name=QualFieldName.BUSINESS_ISSUES,
            field_value=mock_extracted_data.business_issues,
            formatted_field_value=mock_extracted_data.business_issues,
        )

        # Verify the response message
        assert result.system_reply_type == SystemReplyType.FIELD_SAVED
        assert result.is_undoable is False
        assert result.qual_fields is not None

        business_issues_field = getattr(result.qual_fields, QualFieldName.BUSINESS_ISSUES.value)
        assert business_issues_field is not None
        assert business_issues_field.context == mock_extracted_data.business_issues

        summary_field = getattr(result.qual_fields, QualFieldName.ENGAGEMENT_SUMMARY.value)
        assert summary_field is not None
        assert summary_field.context == mock_extracted_data.engagement_summary

        one_line_field = getattr(result.qual_fields, QualFieldName.ONE_LINE_DESCRIPTION.value)
        assert one_line_field is not None
        assert one_line_field.context == mock_extracted_data.one_line_description

    @pytest.mark.disable_autouse
    async def test_handle_client_name_replacement_success(self, command_service: CommandService):
        """Test successful client name replacement."""
        conversation_id = uuid4()
        client_name = 'Test Client'

        # Mock confirmed data
        confirmed_data = ConfirmedData(client_name=client_name)
        command_service.conversation_repository.get_confirmed_data = AsyncMock(return_value=confirmed_data)

        # Mock field values
        fields = {
            QualFieldName.BUSINESS_ISSUES: 'Business issues for Test Client',
            QualFieldName.SCOPE_APPROACH: 'Scope for Test Client',
            QualFieldName.VALUE_DELIVERED: 'Value for Test Client',
            QualFieldName.ENGAGEMENT_SUMMARY: 'Summary for Test Client',
            QualFieldName.ONE_LINE_DESCRIPTION: 'One line for Test Client',
        }

        async def get_latest_value_mock(conv_id, field_name):
            if field_name in fields:
                return QualFieldValue(FieldValue=fields[field_name])
            return None

        command_service.field_repository.get_latest_value = AsyncMock(side_effect=get_latest_value_mock)
        command_service.field_repository.create = AsyncMock()

        # Call the service
        result = await command_service._handle_client_name_replacement(conversation_id)

        # Verify the result
        assert result.system_reply_type == SystemReplyType.CLIENT_NAME_REPLACED
        assert client_name in result.content
        assert result.is_undoable is False

        # Verify that all fields were updated
        for field_name in fields:
            command_service.field_repository.create.assert_any_call(
                conversation_id=conversation_id,
                field_name=field_name,
                field_value=fields[field_name].replace(client_name, 'the client'),
                formatted_field_value=fields[field_name].replace(client_name, 'the client'),
            )

    async def test_handle_client_name_replacement_no_client_name_in_fields(self, command_service: CommandService):
        """Test client name replacement when client name is not in any fields."""
        conversation_id = uuid4()
        client_name = 'Test Client'

        # Mock confirmed data
        confirmed_data = ConfirmedData(client_name=client_name)
        command_service.conversation_repository.get_confirmed_data = AsyncMock(return_value=confirmed_data)

        # Mock field values
        fields = {
            QualFieldName.BUSINESS_ISSUES: 'Business issues',
        }

        async def get_latest_value_mock(conv_id, field_name):
            if field_name in fields:
                return QualFieldValue(FieldValue=fields[field_name])
            return None

        command_service.field_repository.get_latest_value = AsyncMock(side_effect=get_latest_value_mock)
        command_service.field_repository.create = AsyncMock()

        # Call the service
        await command_service._handle_client_name_replacement(conversation_id)

        # Verify that create was not called
        command_service.field_repository.create.assert_not_called()

    async def test_handle_client_name_replacement_some_fields_are_none(self, command_service: CommandService):
        """Test client name replacement when some fields are None."""
        conversation_id = uuid4()
        client_name = 'Test Client'

        # Mock confirmed data
        confirmed_data = ConfirmedData(client_name=client_name)
        command_service.conversation_repository.get_confirmed_data = AsyncMock(return_value=confirmed_data)

        # Mock field values
        fields = {
            QualFieldName.BUSINESS_ISSUES: None,
        }

        async def get_latest_value_mock(conv_id, field_name):
            if field_name in fields and fields[field_name] is not None:
                return QualFieldValue(FieldValue=fields[field_name])
            return None

        command_service.field_repository.get_latest_value = AsyncMock(side_effect=get_latest_value_mock)
        command_service.field_repository.create = AsyncMock()

        # Call the service
        await command_service._handle_client_name_replacement(conversation_id)

        # Verify that create was not called
        command_service.field_repository.create.assert_not_called()


class TestTextEditService:
    """Unit tests for the TextEditService class."""

    @pytest.fixture
    def text_edit_service(self):
        """Create a TextEditService instance with mocked dependencies."""
        mock_openai_repository = AsyncMock()
        return TextEditService(openai_repository=mock_openai_repository)

    @pytest.mark.asyncio
    @pytest.mark.parametrize(
        'command_type,expected_result',
        [
            (
                TextEditCommand.EXPAND,
                'expanded and detailed business context with additional information',
            ),
            (
                TextEditCommand.REWRITE,
                'completely rewritten business context with new phrasing',
            ),
            (TextEditCommand.SHORTEN, 'concise business context'),
        ],
    )
    async def test_text_edit_service_different_commands(self, text_edit_service, command_type, expected_result):
        """Test TextEditService with different command types."""
        full_text = 'This is the complete business context that needs editing and improvement.'
        snippet = 'business context that needs editing'
        mock_response = TextEditCommandValidator(value=expected_result)
        text_edit_service.openai_repository.generate_chat_completion = AsyncMock(return_value=mock_response)
        data_to_analyze = {
            'full_text': full_text,
            'snippet': snippet,
            'text_before_snippet': 'Sample business ',
            'text_after_snippet': ' text',
        }
        result = await text_edit_service.text_edit(
            command=command_type,
            data_to_analyze=data_to_analyze,
            temperature=0.1,
            model='gpt-4o',
        )
        text_edit_service.openai_repository.generate_chat_completion.assert_awaited_once()
        call_args = text_edit_service.openai_repository.generate_chat_completion.call_args
        assert call_args[1]['model'] == 'gpt-4o'
        assert call_args[1]['temperature'] == 0.1
        assert call_args[1]['response_format'] == TextEditCommandValidator
        assert result == expected_result

    async def test_text_edit_service_text_splitting(self, text_edit_service):
        """Test that TextEditService correctly splits text around snippet."""
        from unittest.mock import AsyncMock

        full_text = 'The beginning text. The middle snippet to edit. The ending text.'
        snippet = 'middle snippet to edit'
        expected_edited_snippet = 'EDITED middle snippet'
        mock_response = TextEditCommandValidator(value=expected_edited_snippet)
        text_edit_service.openai_repository.generate_chat_completion = AsyncMock(return_value=mock_response)
        data_to_analyze = {
            'full_text': full_text,
            'snippet': snippet,
            'text_before_snippet': 'The beginning text. The ',
            'text_after_snippet': '. The ending text.',
        }
        result = await text_edit_service.text_edit(
            command=TextEditCommand.EXPAND,
            data_to_analyze=data_to_analyze,
        )
        assert result == expected_edited_snippet
        call_args = text_edit_service.openai_repository.generate_chat_completion.call_args
        messages = call_args[1]['messages']
        user_message_content = messages[1]['content']
        assert 'text_before_snippet' in user_message_content
        assert 'text_after_snippet' in user_message_content
        assert 'The beginning text. The ' in user_message_content
        assert '. The ending text.' in user_message_content

    async def test_text_edit_service_snippet_not_found(self, text_edit_service):
        """Test TextEditService behavior when snippet is not found in full text."""
        from unittest.mock import AsyncMock

        full_text = 'This is the complete text without the target.'
        snippet = 'nonexistent snippet'
        mock_response = TextEditCommandValidator(value='edited snippet')
        text_edit_service.openai_repository.generate_chat_completion = AsyncMock(return_value=mock_response)
        data_to_analyze = {
            'full_text': full_text,
            'snippet': snippet,
            'text_before_snippet': 'This is the complete text without the target',
            'text_after_snippet': 'te text without the target.',
        }
        result = await text_edit_service.text_edit(
            command=TextEditCommand.EXPAND,
            data_to_analyze=data_to_analyze,
        )
        assert result == 'edited snippet'

    async def test_text_edit_service_punctuation_spacing_preservation(self, text_edit_service):
        """Test that the enhanced rewrite prompt handles punctuation spacing correctly."""
        from unittest.mock import AsyncMock

        # Test case that addresses the specific issues mentioned: "efficiency,cut" and "acceleratedite"
        full_text = 'Deloitte aims to enhance data processing efficiency, cut processing time by 40% and accelerate the clinical trial results.'
        snippet = ', cut processing time by 40% and accelerate the'
        # Correct response should maintain proper spacing around punctuation and between words
        mock_response = TextEditCommandValidator(value=', reduce processing time by 40% and expedite the')
        text_edit_service.openai_repository.generate_chat_completion = AsyncMock(return_value=mock_response)

        data_to_analyze = {
            'full_text': full_text,
            'snippet': snippet,
            'text_before_snippet': 'Deloitte aims to enhance data processing efficiency',
            'text_after_snippet': ' clinical trial results.',
            'result_length': None,
        }

        result = await text_edit_service.text_edit(
            command=TextEditCommand.REWRITE,
            data_to_analyze=data_to_analyze,
        )

        # Verify the service was called and returned the expected result
        assert result == ', reduce processing time by 40% and expedite the'

        # Verify the prompt was called with the enhanced system prompt
        call_args = text_edit_service.openai_repository.generate_chat_completion.call_args
        messages = call_args[1]['messages']
        system_message_content = messages[0]['content']

        # Check that the enhanced prompt includes punctuation spacing instructions
        assert 'CRITICAL: Punctuation and Mid-Sentence Spacing' in system_message_content
        assert 'ALWAYS maintain proper spacing around punctuation' in system_message_content
        assert 'NEVER merge words across word boundaries' in system_message_content
        assert 'Example 16: CRITICAL ANTI-PATTERN' in system_message_content
        assert 'acceleratedite' in system_message_content  # Should include the anti-pattern example

        # Verify the data structure includes proper punctuation context
        user_message_content = messages[1]['content']
        assert '"text_before_snippet": "Deloitte aims to enhance data processing efficiency"' in user_message_content
        assert '"text_after_snippet": " clinical trial results."' in user_message_content


class TestCommandJSONValidation:
    """Tests for Command model JSON validation."""

    def test_command_json_string_validation_success(self):
        """Test successful JSON string validation for Command model."""
        json_string = json.dumps(
            {
                'command': 'expand',
                'field_name': 'business_issues',
                'context': 'Sample context text',
                'formatted_context': 'Sample context text',
                'snippet': 'context text',
            }
        )

        command = Command.model_validate(json_string)

        assert command.command == TextEditCommand.EXPAND
        assert command.field_name == QualFieldName.BUSINESS_ISSUES
        assert command.context == 'Sample context text'
        assert command.snippet == 'context text'

    def test_command_dict_validation_success(self):
        """Test successful dictionary validation for Command model."""
        command_dict = {
            'command': 'rewrite',
            'field_name': 'scope_approach',
            'context': 'Sample scope text',
            'formatted_context': 'Sample scope text',
            'snippet': 'scope text',
        }

        command = Command.model_validate(command_dict)

        assert command.command == TextEditCommand.REWRITE
        assert command.field_name == QualFieldName.SCOPE_APPROACH
        assert command.context == 'Sample scope text'
        assert command.snippet == 'scope text'

    def test_command_invalid_json_string(self):
        """Test Command model validation with invalid JSON string."""
        invalid_json = '{"command": "expand", "field_name": "business_issues"'  # Missing closing brace

        with pytest.raises(ValueError, match='Invalid JSON string for command'):
            Command.model_validate(invalid_json)

    def test_command_missing_required_fields(self):
        """Test Command model validation with missing required fields."""
        incomplete_data = {
            'command': 'expand',
            'field_name': 'business_issues',
            # Missing 'context' and 'snippet'
        }

        with pytest.raises(ValueError):
            Command.model_validate(incomplete_data)

    def test_command_invalid_enum_values(self):
        """Test Command model validation with invalid enum values."""
        invalid_command_data = {
            'command': 'invalid_command',
            'field_name': 'business_issues',
            'context': 'Sample context',
            'formatted_context': 'Sample context',
            'snippet': 'context',
        }

        with pytest.raises(ValueError):
            Command.model_validate(invalid_command_data)

        invalid_field_data = {
            'command': 'expand',
            'field_name': 'invalid_field',
            'context': 'Sample context',
            'formatted_context': 'Sample context',
            'snippet': 'context',
        }

        with pytest.raises(ValueError):
            Command.model_validate(invalid_field_data)


class TestPromptTextEditFunctionality:
    """Tests for the new PROMPT text edit functionality."""

    @pytest.fixture
    def command_service(self):
        """Create a CommandService instance with mocked dependencies."""
        mock_field_value_repository = AsyncMock()
        mock_text_edit_service = AsyncMock()
        mock_openai_repository = AsyncMock()
        mock_conversation_repository = AsyncMock()
        mock_extracted_data_service = AsyncMock()
        return CommandService(
            field_repository=mock_field_value_repository,
            text_edit_service=mock_text_edit_service,
            openai_repository=mock_openai_repository,
            conversation_repository=mock_conversation_repository,
            extracted_data_service=mock_extracted_data_service,
        )

    @pytest.fixture
    def prompt_command(self) -> Command:
        """Create a sample PROMPT command."""
        return Command(
            command=TextEditCommand.PROMPT,
            field_name=QualFieldName.BUSINESS_ISSUES,
            context='The business is facing operational challenges and needs process improvements.',
            formatted_context='The business is facing operational challenges and needs process improvements.',
            snippet='operational challenges',
        )

    async def test_prompt_command_includes_user_request_in_data_to_analyze(
        self,
        command_service: CommandService,
        prompt_command: Command,
    ):
        """Test that PROMPT commands include user_request in data_to_analyze."""
        conversation_id = uuid4()
        user_content = 'Please make this more professional and add specific examples of challenges.'

        # Mock the text edit service
        command_service.text_edit_service.text_edit = AsyncMock(
            return_value='professional operational challenges with specific examples'
        )

        # Call the service
        result = await command_service.process_command(prompt_command, user_content, conversation_id)

        # Verify the text edit service was called with data that includes user_request
        command_service.text_edit_service.text_edit.assert_called_once()
        call_args = command_service.text_edit_service.text_edit.call_args

        # Check that data_to_analyze includes user_request
        data_to_analyze = call_args[1]['data_to_analyze']
        assert 'user_request' in data_to_analyze
        assert data_to_analyze['user_request'] == user_content
        original_context = 'The business is facing operational challenges and needs process improvements.'
        assert data_to_analyze['full_text'] == original_context
        original_snippet = 'operational challenges'
        assert data_to_analyze['snippet'] == original_snippet

        # Verify the result
        assert isinstance(result, MessageValidator)
        assert result.conversation_id == conversation_id
        if result.command is not None:
            assert result.command.command == TextEditCommand.PROMPT

    async def test_non_prompt_command_excludes_user_request(
        self,
        command_service: CommandService,
    ):
        """Test that non-PROMPT commands do not include user_request in data_to_analyze."""
        conversation_id = uuid4()
        user_content = 'This content should not be included for non-PROMPT commands.'

        # Create a non-PROMPT command
        expand_command = Command(
            command=TextEditCommand.EXPAND,
            field_name=QualFieldName.BUSINESS_ISSUES,
            context='The business needs expansion.',
            formatted_context='The business needs expansion.',
            snippet='business needs',
        )

        # Mock the text edit service
        command_service.text_edit_service.text_edit = AsyncMock(return_value='expanded business needs')

        # Call the service
        await command_service.process_command(expand_command, user_content, conversation_id)

        # Verify the text edit service was called with data that excludes user_request
        command_service.text_edit_service.text_edit.assert_called_once()
        call_args = command_service.text_edit_service.text_edit.call_args

        # Check that data_to_analyze does NOT include user_request
        data_to_analyze = call_args[1]['data_to_analyze']
        assert 'user_request' not in data_to_analyze
        # The full_text should be the original context, not the modified one
        original_context = 'The business needs expansion.'
        assert data_to_analyze['full_text'] == original_context
        # The snippet should be the original snippet, not the modified one
        original_snippet = 'business needs'
        assert data_to_analyze['snippet'] == original_snippet

    async def test_prompt_command_api_integration(
        self,
        auth_mock,
        auth_header,
        async_client: CustomAsyncClient,
        url_resolver: URLResolver,
        test_conversation_id,
    ):
        """Test PROMPT command processing through the API."""
        # Prepare PROMPT command data
        original_context = 'The company has operational inefficiencies that need to be addressed.'
        snippet = 'operational inefficiencies'
        user_content = 'Please make this more detailed and add specific examples of inefficiencies.'

        command_data = {
            'command': TextEditCommand.PROMPT.value,
            'field_name': QualFieldName.BUSINESS_ISSUES.value,
            'context': original_context,
            'formatted_context': original_context,
            'snippet': snippet,
        }

        # Mock the TextEditService response
        expected_edited_snippet = 'detailed operational inefficiencies with specific examples'

        with patch('services.text_edit.TextEditService.text_edit', new_callable=AsyncMock) as mock_text_edit:
            mock_text_edit.return_value = expected_edited_snippet

            # Create message with PROMPT command
            message_url = url_resolver.reverse(operation_ids.message.CREATE)
            form_data = {
                'conversation_id': str(test_conversation_id),
                'content': user_content,  # This content should be passed to the command service
                'command': json.dumps(command_data),
                'page_type': PageType.ENGAGEMENT_DESCRIPTION.value,
            }

            response = await async_client.post(message_url, headers=auth_header, data=form_data)

        # Verify response
        assert response.status_code == status.HTTP_201_CREATED
        data = response.json()

        # Check that the command was processed
        assert 'command' in data['user']
        returned_command = data['user']['command']
        assert returned_command['command'] == TextEditCommand.PROMPT.value
        assert returned_command['field_name'] == QualFieldName.BUSINESS_ISSUES.value

        # Verify the text edit service was called with user_request
        mock_text_edit.assert_called_once()
        call_args = mock_text_edit.call_args
        data_to_analyze = call_args[1]['data_to_analyze']
        assert 'user_request' in data_to_analyze
        assert data_to_analyze['user_request'] == user_content

    async def test_prompt_command_with_empty_user_content(
        self,
        command_service: CommandService,
        prompt_command: Command,
    ):
        """Test PROMPT command behavior when user content is empty."""
        conversation_id = uuid4()
        empty_content = ''

        # Mock the text edit service
        command_service.text_edit_service.text_edit = AsyncMock(return_value='edited text')

        # Call the service
        await command_service.process_command(prompt_command, empty_content, conversation_id)

        # Verify the text edit service was called with empty user_request
        command_service.text_edit_service.text_edit.assert_called_once()
        call_args = command_service.text_edit_service.text_edit.call_args
        data_to_analyze = call_args[1]['data_to_analyze']
        assert 'user_request' in data_to_analyze
        assert data_to_analyze['user_request'] == ''

    async def test_prompt_command_snippet_not_found_handling(
        self,
        command_service: CommandService,
    ):
        """Test PROMPT command behavior when snippet is not found in context."""
        conversation_id = uuid4()
        user_content = 'Please improve this text.'

        # Create a command where snippet is not in context
        prompt_command = Command(
            command=TextEditCommand.PROMPT,
            field_name=QualFieldName.BUSINESS_ISSUES,
            context='The business is doing well.',
            formatted_context='The business is doing well.',
            snippet='nonexistent snippet',  # This snippet is not in the context
        )

        # Mock the text edit service
        command_service.text_edit_service.text_edit = AsyncMock(return_value='edited text')

        # Call the service
        await command_service.process_command(prompt_command, user_content, conversation_id)

        # Verify the text edit service was called with data that includes user_request even when snippet not found
        command_service.text_edit_service.text_edit.assert_called_once()
        call_args = command_service.text_edit_service.text_edit.call_args
        data_to_analyze = call_args[1]['data_to_analyze']
        assert 'user_request' in data_to_analyze
        assert data_to_analyze['user_request'] == user_content
        assert data_to_analyze['text_before_snippet'] == ''
        assert data_to_analyze['text_after_snippet'] == ''

    @pytest.mark.parametrize(
        'command_type,should_include_user_request',
        [
            (TextEditCommand.PROMPT, True),
            (TextEditCommand.EXPAND, False),
            (TextEditCommand.REWRITE, False),
            (TextEditCommand.SHORTEN, False),
            (TextEditCommand.STORE, False),
        ],
    )
    async def test_user_request_inclusion_by_command_type(
        self,
        command_service: CommandService,
        command_type: TextEditCommand,
        should_include_user_request: bool,
    ):
        """Test that user_request is only included for PROMPT commands."""
        conversation_id = uuid4()
        user_content = 'User guidance for text editing.'

        # Create command with specified type
        command = Command(
            command=command_type,
            field_name=QualFieldName.BUSINESS_ISSUES,
            context='Sample business context.',
            formatted_context='Sample business context.',
            snippet='business context',
        )

        # Mock the text edit service
        command_service.text_edit_service.text_edit = AsyncMock(return_value='edited text')

        # Call the service
        await command_service.process_command(command, user_content, conversation_id)

        # For STORE commands, text_edit_service should not be called
        if command_type == TextEditCommand.STORE:
            command_service.text_edit_service.text_edit.assert_not_called()
        else:
            # Verify the text edit service was called
            command_service.text_edit_service.text_edit.assert_called_once()
            call_args = command_service.text_edit_service.text_edit.call_args
            data_to_analyze = call_args[1]['data_to_analyze']

            if should_include_user_request:
                assert 'user_request' in data_to_analyze
                assert data_to_analyze['user_request'] == user_content
            else:
                assert 'user_request' not in data_to_analyze

    async def test_formatting_intent_returns_sorry_template(
        self,
        auth_mock,
        auth_header,
        async_client: CustomAsyncClient,
        url_resolver: URLResolver,
        test_conversation_id,
    ):
        """Test that a formatting intent returns a sorry template."""
        with patch(
            'services.message_handlers.engagement_description_handler.EngagementDescriptionMessageHandler._classify_intent',
            new_callable=AsyncMock,
        ) as mock_classify_intent:
            mock_classify_intent.return_value = EngagementMessageIntention.FORMATTING

            # Create message with command
            message_url = url_resolver.reverse(operation_ids.message.CREATE)
            form_data = {
                'conversation_id': str(test_conversation_id),
                'content': 'make it bolder',
                'page_type': PageType.ENGAGEMENT_DESCRIPTION.value,
            }

            response = await async_client.post(message_url, headers=auth_header, data=form_data)

            # Verify response
            assert response.status_code == status.HTTP_201_CREATED
            data = response.json()

            # Check system message structure
            assert data['system']['role'] == str(MessageRole.SYSTEM)
            assert data['system']['type'] == str(MessageType.TEXT)
            assert data['system']['system_reply_type'] == str(SystemReplyType.ENGAGEMENT_DETAILS_SORRY_CANT_HELP)
            assert data['system']['content'] == engagement_templates.general.sorry_cant_help_reply
