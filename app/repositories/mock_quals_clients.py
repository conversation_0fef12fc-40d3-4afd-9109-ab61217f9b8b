import logging
from uuid import uuid4

from schemas import (
    ClientAPIParamsItem,
    ClientByIdResponse,
    ClientComprehensive,
    ClientConfidentialityItem,
    ClientCreateRequest,
    ClientCreateResponse,
    ClientParent,
    ClientSearchItem,
    ClientSearchRequest,
    ClientSearchResponse,
    ClientStatus,
    RelatedClientsResponse,
)


__all__ = ['MockQualsClientsRepository']


logger = logging.getLogger(__name__)


class MockQualsClientsRepository:
    """Mock repository for Quals Clients API operations for testing and development."""

    MOCK_CLIENT_DATA = {
        'amazon': {
            'parent': ClientSearchItem(id=1001, name='Amazon', qualsCount=15, clientConfidentiality=1),
            'subsidiaries': [
                ClientSearchItem(id=1002, name='Amazon Web Services', qualsCount=8, clientConfidentiality=1),
                ClientSearchItem(id=1003, name='Amazon Prime', qualsCount=5, clientConfidentiality=1),
                ClientSearchItem(id=1004, name='Amazon Studios', qualsCount=3, clientConfidentiality=1),
            ],
        },
        'biogen': {
            'parent': ClientSearchItem(id=2001, name='BioGen Corp', qualsCount=12, clientConfidentiality=1),
            'subsidiaries': [
                ClientSearchItem(id=2002, name='BioGen International', qualsCount=7, clientConfidentiality=1),
                ClientSearchItem(id=2003, name='BioGen Research', qualsCount=4, clientConfidentiality=1),
            ],
        },
        'microsoft': {
            'parent': ClientSearchItem(id=3001, name='Microsoft Corporation', qualsCount=20, clientConfidentiality=1),
            'subsidiaries': [
                ClientSearchItem(id=3002, name='Microsoft Azure', qualsCount=10, clientConfidentiality=1),
                ClientSearchItem(id=3003, name='Microsoft Office', qualsCount=6, clientConfidentiality=1),
            ],
        },
    }

    def _generate_mock_search_results(self, search_request: ClientSearchRequest) -> list[ClientSearchItem]:
        """Generate mock search results for testing, including subsidiaries for specific companies."""
        search_term = search_request.contains.lower()

        mock_clients = []
        # Check for specific company names in the search term
        for company, data in self.MOCK_CLIENT_DATA.items():
            if company in search_term:
                mock_clients = [data['parent']] + data['subsidiaries']
                break  # Stop after the first match to mimic if/elif
        else:  # This 'else' belongs to the 'for' loop, executes if no break
            # Default generic mock results
            mock_clients = [
                ClientSearchItem(
                    id=1, name=f'Global Corp {search_request.contains}', qualsCount=15, clientConfidentiality=1
                ),
                ClientSearchItem(
                    id=2, name=f'International {search_request.contains} Ltd', qualsCount=8, clientConfidentiality=1
                ),
                ClientSearchItem(
                    id=3, name=f'{search_request.contains} Industries Inc', qualsCount=23, clientConfidentiality=1
                ),
            ]

        start_idx = search_request.page_idx * search_request.page_size
        end_idx = start_idx + search_request.page_size
        mock_clients = self._unique_list(mock_clients)
        mock_clients.sort(key=lambda client: client.id)
        return mock_clients[start_idx:end_idx]

    def _generate_mock_client_comprehensive(self, create_request: ClientCreateRequest) -> ClientComprehensive:
        """Generate a mock comprehensive client object for testing."""
        client_id = int(uuid4().int >> 96)
        return ClientComprehensive(
            id=client_id,
            name=create_request.name,
            description=f'Mock description for {create_request.name}',
            primaryLocalIndustry='Technology',
            primaryGlobalIndustry='Information Technology',
            secondaryLocalIndustries=['Software Development', 'Consulting'],
            secondaryGlobalIndustries=['Digital Transformation', 'Cloud Services'],
        )

    def _generate_mock_client_by_id(self, client_id: int) -> ClientByIdResponse:
        """Generate a fake mock ClientByIdResponse for testing."""
        return ClientByIdResponse(
            id=client_id,
            name='Searched Client',
            parent=ClientParent(id=99999, name='Some Parent'),
            status=ClientStatus(id=1, name='Active'),
        )

    def _generate_mock_related_clients(self, client_id: int) -> RelatedClientsResponse:
        """Generate a static fake RelatedClientsResponse for testing."""
        parent = ClientParent(id=12345, name='Fake Parent Company')
        subsidiaries = [
            ClientParent(id=20001, name='Fake Subsidiary A'),
            ClientParent(id=20002, name='Fake Subsidiary B'),
            ClientParent(id=20003, name='Fake Subsidiary C'),
            ClientParent(id=20004, name='Fake Subsidiary D'),
            ClientParent(id=20005, name='Fake Subsidiary E'),
        ]
        return RelatedClientsResponse(parent=parent, subsidiaries=subsidiaries)

    async def _enrich_with_related_clients_mock(
        self, clients: list[ClientSearchItem], token: str
    ) -> list[ClientSearchItem]:
        """Mock version of the subsidiary enrichment logic from the real repository."""
        seen_ids = {client.id for client in clients}
        parent_id_to_subs = {data['parent'].id: data['subsidiaries'] for data in self.MOCK_CLIENT_DATA.values()}
        subsidiaries = [
            sub
            for client in clients
            if client.id in parent_id_to_subs
            for sub in parent_id_to_subs[client.id]
            if sub.id not in seen_ids
        ]
        return clients + subsidiaries

    async def search_clients(self, search_request: ClientSearchRequest, token: str) -> ClientSearchResponse:
        """Search for clients using mock data, including subsidiary enrichment."""
        logger.info('Using mock data for client search: %s', search_request.contains)
        mock_results = self._generate_mock_search_results(search_request)

        # Simulate the subsidiary enrichment logic from the real repository
        enriched_results = await self._enrich_with_related_clients_mock(mock_results, token)

        exact_match = search_request.contains.lower() in {client.name.lower() for client in enriched_results}

        return ClientSearchResponse(
            clients=enriched_results,
            total_count=len(enriched_results) + 10,
            page_size=search_request.page_size,
            page_idx=search_request.page_idx,
            exact_match=exact_match,
        )

    async def create_client(self, create_request: ClientCreateRequest, token: str) -> ClientCreateResponse:
        """Create a new client using mock data."""
        logger.info('Using mock data for client creation: %s', create_request.name)
        mock_client = self._generate_mock_client_comprehensive(create_request)
        return ClientCreateResponse(client=mock_client, success=True, message='Client created successfully (mock)')

    async def get_references_list(self, token: str) -> list[ClientAPIParamsItem]:
        """Get a list of references using mock data."""
        logger.info('Using mock data for references list')
        return [
            ClientAPIParamsItem(id=868, name='Yes', catalogue_number=7, catalogue_type=7, sort_order=1),
            ClientAPIParamsItem(id=985, name='No', catalogue_number=7, catalogue_type=7, sort_order=2),
            ClientAPIParamsItem(id=869, name='In some cases', catalogue_number=7, catalogue_type=7, sort_order=3),
            ClientAPIParamsItem(id=867, name="I don't know", catalogue_number=7, catalogue_type=7, sort_order=4),
        ]

    async def get_client_sharing_list(self, token: str) -> list[ClientAPIParamsItem]:
        """Get a list of client sharing options using mock data."""
        logger.info('Using mock data for client_sharing list')
        return [
            ClientAPIParamsItem(
                catalogue_number=69,
                catalogue_type=69,
                sort_order=0,
                name='Client name may be shared internally',
                id=879,
            ),
            ClientAPIParamsItem(
                catalogue_number=69,
                catalogue_type=69,
                sort_order=0,
                name='Client name may NOT be shared internally',
                id=880,
            ),
        ]

    async def get_client_confidentiality_list(self, token: str) -> list[ClientConfidentialityItem]:
        """Get a list of client confidentiality options using mock data."""
        logger.info('Using mock data for client_confidentiality list')
        return [
            ClientConfidentialityItem(id=0, name='NotSet'),
            ClientConfidentialityItem(id=1, name='Standard'),
            ClientConfidentialityItem(id=2, name='Extensive'),
        ]

    async def get_client_by_id(self, client_id: int, token: str) -> ClientByIdResponse:
        """Get client details by clientId using mock data."""
        logger.info(f'Returning mock client by id: {client_id}')
        return self._generate_mock_client_by_id(client_id)

    async def get_related_clients(self, client_id: int, token: str) -> RelatedClientsResponse:
        """Get related clients for a given client ID using mock data."""
        logger.info(f'Returning mock related clients for id: {client_id}')
        return self._generate_mock_related_clients(client_id)

    def _unique_list(self, clients: list[ClientSearchItem]) -> list[ClientSearchItem]:
        """Return a list of clients with unique names."""
        unique_clients = {}
        for client in clients:
            unique_clients.setdefault(client.name, client)
        return list(unique_clients.values())
