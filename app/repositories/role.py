import logging
from typing import List

from config import settings
from core.http_client import CustomAsyncClient
from core.urls import url_join
from schemas import ProjectRolesData
from schemas.team_member import TeamMemberGraphItem


__all__ = ['RoleRepository']

logger = logging.getLogger(__name__)


class RoleRepository:
    """Repository for Role API operations (roles and team members graph)."""

    def __init__(self, http_client: CustomAsyncClient):
        """Initialize the Role Repository with an HTTP client."""
        self._http_client = http_client
        self._base_path = str(settings.roles_api.base_url)

    async def list(self, token: str) -> List[ProjectRolesData]:
        """List all project roles."""
        url = url_join(self._base_path, 'project-roles')
        headers = {'Authorization': f'Bearer {token}'} if token else {}

        response = await self._http_client.get(url, headers=headers)
        data = response.json()

        project_roles = [ProjectRolesData(**role) for role in data]
        return project_roles

    async def search_team_members(self, contains: str, token: str) -> List[TeamMemberGraphItem]:
        """Search team members via team-members-graph endpoint by name substring."""
        url = url_join(self._base_path, 'team-members-graph')
        headers = {'Authorization': f'Bearer {token}'} if token else {}
        params = {'contains': contains}

        response = await self._http_client.get(url, headers=headers, params=params)
        data = response.json()

        # Expecting list of items
        items = [TeamMemberGraphItem.model_validate(item) for item in (data or [])]
        return items
