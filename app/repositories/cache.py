import logging
from typing import cast

from cachetools import TTLCache


__all__ = ['InMemoryCacheRepository']

logger = logging.getLogger(__name__)


class InMemoryCacheRepository[T]:
    """In-memory cache repository implementation using TTLCache."""

    def __init__(
        self,
        maxsize: int = 100,
        ttl: int = 3600,
        key_prefix: str = '',
    ):
        """
        Initialize the in-memory cache repository.

        Args:
            maxsize: Maximum number of items in the cache
            ttl: Default time to live in seconds
            key_prefix: Prefix to add to all cache keys for namespace isolation
        """
        self._cache = TTLCache(maxsize=maxsize, ttl=ttl)
        self._default_ttl = ttl
        self._key_prefix = key_prefix

    def _make_key(self, key: str) -> str:
        """Create a prefixed cache key."""
        return f'{self._key_prefix}{key}' if self._key_prefix else key

    async def get(self, key: str) -> T | None:
        """Get a value from the cache by key."""
        cache_key = self._make_key(key)
        try:
            value = self._cache.get(cache_key)
            return cast(T, value) if value is not None else None
        except Exception as e:
            logger.error("Error getting value from cache for key '%s': %s", key, e)
            return None

    async def set(self, key: str, value: T) -> None:
        """Set a value in the cache."""
        cache_key = self._make_key(key)
        try:
            self._cache[cache_key] = value
        except Exception as e:
            logger.error("Error setting value in cache for key '%s': %s", key, e)
            raise

    async def remove(self, key: str) -> bool:
        """Remove a value from the cache."""
        cache_key = self._make_key(key)
        try:
            if cache_key in self._cache:
                del self._cache[cache_key]
                return True
            return False
        except Exception as e:
            logger.error("Error removing value from cache for key '%s': %s", key, e)
            return False

    async def clear(self) -> None:
        """Clear all values from the cache."""
        try:
            self._cache.clear()
        except Exception as e:
            logger.error('Error clearing cache: %s', e)
            raise
