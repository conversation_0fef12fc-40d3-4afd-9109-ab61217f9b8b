import asyncio
import inspect
import logging
from typing import Any, Type, TypeVar, cast, overload

from config import settings
from constants.cache_keys import Cache<PERSON>eys
from core.http_client import CustomAsyncClient
from core.urls import url_join
from repositories.cache import InMemoryCacheRepository
from schemas import (
    ClientAPIParamsItem,
    ClientByIdResponse,
    ClientComprehensive,
    ClientConfidentialityItem,
    ClientCreateRequest,
    ClientCreateResponse,
    ClientParent,
    ClientSearchItem,
    ClientSearchRequest,
    ClientSearchResponse,
    ClientStatus,
    RelatedClientsResponse,
)
from schemas.extracted_data import IndustryData


__all__ = ['QualsClientsRepository']

logger = logging.getLogger(__name__)

T = TypeVar('T')


class QualsClientsRepository:
    """Repository for Quals Clients API operations."""

    def __init__(self, http_client: CustomAsyncClient, cache_repository: InMemoryCacheRepository):
        """
        Initialize the Quals Clients Repository.

        Args:
            http_client: The CustomAsyncClient to use for requests.
            cache_repository: The cache repository for caching.
        """
        self._base_url = str(settings.quals_clients_api.base_url)
        self._http_client = http_client
        self._cache_repository = cache_repository

    @overload
    async def _send_request(
        self, method: str, endpoint: str, params: dict | None = None, token: str | None = None, model: None = None
    ) -> Any: ...

    @overload
    async def _send_request(
        self, method: str, endpoint: str, params: dict | None = None, token: str | None = None, *, model: Type[T]
    ) -> T | list[T]: ...

    async def _send_request(
        self,
        method: str,
        endpoint: str,
        params: dict | None = None,
        token: str | None = None,
        model: Type[T] | None = None,
    ) -> Any:
        """Send an HTTP request and parse the response."""
        url = url_join(self._base_url, endpoint)
        headers = {'Authorization': f'Bearer {token}'} if token else {} if token else {}
        try:
            response = await self._http_client.request(method, url, params=params, headers=headers)
            rfs = response.raise_for_status()
            if inspect.isawaitable(rfs):
                await rfs

            response_data = response.json()
            if inspect.isawaitable(response_data):
                response_data = await response_data

            if model:
                if isinstance(response_data, list):
                    return [model(**item) for item in response_data]
                return model(**response_data)
            return response_data
        except Exception as e:
            logger.exception(f'Error during request to {url}: {e}')
            raise

    async def search_clients(self, search_request: ClientSearchRequest, token: str) -> ClientSearchResponse:
        """Search for clients using the Quals Clients API."""
        try:
            clients, total_count = await self._fetch_and_parse_clients(search_request, token)
            clients = self._unique_list(clients)
            exact_match = any(client.name.lower() == search_request.contains.lower() for client in clients)
            active_clients = await self._filter_active_clients(clients, token)
            clients = await self._enrich_with_related_clients(clients, active_clients, token)
            return ClientSearchResponse(
                clients=clients,
                total_count=total_count,
                page_size=search_request.page_size,
                page_idx=search_request.page_idx,
                exact_match=exact_match,
            )
        except Exception:
            logger.exception('Error searching clients')
            raise

    async def _fetch_and_parse_clients(self, search_request: ClientSearchRequest, token: str) -> tuple[list, int]:
        """Fetch and parse clients from the API."""
        params = {
            'contains': search_request.contains,
            'pageSize': search_request.page_size,
            'pageIdx': search_request.page_idx,
        }
        response_data = await self._send_request('get', 'client/clients', params=params, token=token)
        if isinstance(response_data, list):
            clients = [ClientSearchItem(**client) for client in response_data]
            total_count = len(clients)
        else:
            clients = [ClientSearchItem(**client) for client in response_data.get('clients', [])]
            total_count = response_data.get('totalCount', len(clients))
        clients.sort(key=lambda client: client.id)
        return clients, total_count

    async def _filter_active_clients(self, clients: list, token: str) -> list:
        """Filter a list of clients to include only active ones."""
        tasks = [self.get_client_by_id(client.id, token) for client in clients]
        clients_info = await asyncio.gather(*tasks)
        return [client for client in clients_info if client and client.status and client.status.name == 'Active']

    async def _enrich_with_related_clients(self, clients: list, active_clients: list, token: str) -> list:
        """Enrich the client list with related clients (parents and subsidiaries)."""
        # Initialize seen set with ALL existing clients (both active and inactive) to prevent duplicates
        seen = {client.id for client in clients if hasattr(client, 'id') and client.id}
        tasks = [self.get_related_clients(client.id, token) for client in active_clients if client.id]
        related_clients_list = await asyncio.gather(*tasks)

        for related_clients in related_clients_list:
            if (
                related_clients.parent
                and related_clients.parent.id
                and related_clients.parent.name
                and related_clients.parent.id not in seen
            ):
                clients.append(
                    ClientSearchItem(
                        id=related_clients.parent.id,
                        name=related_clients.parent.name,
                        qualsCount=0,
                        clientConfidentiality=1,
                    )
                )
                seen.add(related_clients.parent.id)
            if related_clients.subsidiaries:
                for sub in related_clients.subsidiaries:
                    if sub.id and sub.name and sub.id not in seen:
                        clients.append(
                            ClientSearchItem(id=sub.id, name=sub.name, qualsCount=0, clientConfidentiality=1)
                        )
                        seen.add(sub.id)
        return clients

    def _unique_list(self, clients: list[ClientSearchItem]) -> list[ClientSearchItem]:
        """Return a list of clients with unique names."""
        unique_clients = {client.name: client for client in clients}
        return list(unique_clients.values())

    async def create_client(self, create_request: ClientCreateRequest, token: str) -> ClientCreateResponse:
        """Create a new client."""
        params = {'name': create_request.name}
        try:
            response_data = await self._send_request('post', 'clients', params=params, token=token)
            client = ClientComprehensive(**response_data)
            return ClientCreateResponse(client=client, success=True, message='Client created successfully')
        except Exception:
            logger.exception(f"Error creating client '{create_request.name}'")
            raise

    async def get_references_list(self, token: str) -> list[ClientAPIParamsItem]:
        """Get a list of references from the Quals Clients API."""
        cached_items = await self._cache_repository.get(CacheKeys.REFERENCES)
        if cached_items:
            return cached_items
        items = await self._send_request('get', 'client/client-references', token=token, model=ClientAPIParamsItem)
        items = cast(list[ClientAPIParamsItem], items)
        await self._cache_repository.set(CacheKeys.REFERENCES, items)
        return items

    async def get_client_sharing_list(self, token: str) -> list[ClientAPIParamsItem]:
        """Get a list of client sharing options."""
        cached_items = await self._cache_repository.get(CacheKeys.NAME_SHARING)
        if cached_items:
            return cached_items
        items = await self._send_request('get', 'client/internal-using', token=token, model=ClientAPIParamsItem)
        items = cast(list[ClientAPIParamsItem], items)
        await self._cache_repository.set(CacheKeys.NAME_SHARING, items)
        return items

    async def get_client_confidentiality_list(self, token: str) -> list[ClientConfidentialityItem]:
        """Get a list of client confidentiality options."""
        cached_items = await self._cache_repository.get(CacheKeys.CONFIDENTIALITY)
        if cached_items:
            return cached_items
        items = await self._send_request('get', 'client/confidentiality', token=token, model=ClientConfidentialityItem)
        items = cast(list[ClientConfidentialityItem], items)
        await self._cache_repository.set(CacheKeys.CONFIDENTIALITY, items)
        return items

    async def get_client_by_id(self, client_id: int, token: str) -> ClientByIdResponse:
        """Get client details by ID."""
        try:
            response_data = await self._send_request(
                'get', 'client/getclient', params={'clientId': client_id}, token=token
            )
            parent = ClientParent(**response_data['parent']) if response_data.get('parent') else None
            status = ClientStatus(**response_data['status']) if response_data.get('status') else None
            return ClientByIdResponse(
                id=response_data.get('id'), name=response_data.get('name'), parent=parent, status=status
            )
        except Exception:
            logger.exception(f'Error fetching client by id {client_id}')
            return ClientByIdResponse(id=None, name=None, parent=None, status=None)

    async def get_related_clients(self, client_id: int, token: str) -> RelatedClientsResponse:
        """Get related clients (parent and subsidiaries)."""
        try:
            response_data = await self._send_request('get', f'client/{client_id}/related', token=token)
            parent = ClientParent(**response_data['parent']) if response_data.get('parent') else None
            subsidiaries = (
                [ClientParent(**sub) for sub in response_data['subsidiaries']]
                if response_data.get('subsidiaries')
                else None
            )
            return RelatedClientsResponse(parent=parent, subsidiaries=subsidiaries)
        except Exception:
            logger.exception(f'Error fetching related clients for id {client_id}')
            return RelatedClientsResponse(parent=None, subsidiaries=None)

    async def get_client_industries(self, client_id: int, token: str) -> list[ClientAPIParamsItem]:
        """Get a list of client industries."""
        response_data = await self._send_request(
            'get', 'client/information', params={'clientId': client_id}, token=token
        )
        result = []

        if primary_industry := response_data.get('primaryLocalIndustry'):
            if name := primary_industry.get('name'):
                result.append(IndustryData(name=name, ldmf_country='Global', is_primary=True))

        if secondary_industries := response_data.get('secondaryLocalIndustries'):
            result.extend(
                IndustryData(name=name, ldmf_country='Global', is_primary=False)
                for industry in secondary_industries
                if (name := industry.get('name'))
            )

        if result and not any(client_industry.is_primary for client_industry in result):
            result[0].is_primary = True

        return result
