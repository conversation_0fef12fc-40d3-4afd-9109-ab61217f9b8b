import logging
from typing import Sequence
from uuid import UUID

from sqlalchemy import func, select, update
from sqlalchemy.ext.asyncio import AsyncSession

from exceptions import EntityNotFoundError
from models.qual_conflict import QualConflict
from models.qual_conversation import QualConversation

from .conversation import ConversationRepository


__all__ = ['ConflictRepository']


logger = logging.getLogger(__name__)


class ConflictRepository:
    """Repository for conflicts database operations."""

    def __init__(self, db_session: AsyncSession, conversation_repository: ConversationRepository):
        self.db_session = db_session
        self.conversation_repository = conversation_repository

    async def get_conversation_conflicts(
        self,
        conversation_id: UUID,
        field: str | None = None,
        description: str | None = None,
        exclude_resolved: bool = False,
    ) -> Sequence[QualConflict]:
        """
        Get all conflicts for a specific conversation.
        Args:
            conversation_id: The ID of the conversation
            field: Optional field to filter conflicts
            description: Optional description to filter conflicts
            exclude_resolved: If True, only unresolved conflicts are returned
        Returns:
            list[QualConflict]: List of conflicts for the conversation
        """
        conversation_internal_id = await self.conversation_repository.get_internal_id(conversation_id)
        if conversation_internal_id is None:
            raise EntityNotFoundError('Conversation', str(conversation_id))

        query = select(QualConflict).where(QualConflict.ConversationId == conversation_internal_id)
        if field is not None:
            query = query.where(QualConflict.Field == field)
        if description is not None:
            query = query.where(QualConflict.Description == description)
        if exclude_resolved:
            query = query.where(QualConflict.ChosenValue.is_(None))

        results = (await self.db_session.execute(query)).scalars().all()
        return results

    async def update_chosen_value(self, id: int, chosen_value: str) -> None:
        """
        Update the chosen value of a conflict.
        Args:
            id: The ID of the conflict
            chosen_value: The value chosen to resolve the conflict
        """
        logger.debug('Updating chosen_value conflict ID: %s to %s', id, chosen_value)
        query = update(QualConflict).where(QualConflict.Id == id).values(ChosenValue=chosen_value)
        await self.db_session.execute(query)

    async def has_unresolved_conflicts(self, conversation_id: UUID) -> bool:
        """
        Check if a conversation has any conflicts where chosen_value is NULL.
        """

        stmt = (
            select(func.count())
            .select_from(QualConflict)
            .join(QualConversation, QualConflict.ConversationId == QualConversation.Id)
            .where(QualConversation.PublicId == conversation_id, QualConflict.ChosenValue.is_(None))
        )
        result = await self.db_session.execute(stmt)
        return result.scalar_one() > 0

    async def check_conversation_conflicts(self, conversation_id: UUID) -> bool | None:
        """
        Check if conflicts are resolved for a conversation.

        Returns:
            None  -> no conflicts at all
            False -> some conflicts exist with ChosenValue IS NULL
            True  -> conflicts exist and all have ChosenValue NOT NULL
        """
        conversation_internal_id = await self.conversation_repository.get_internal_id(conversation_id)
        if conversation_internal_id is None:
            raise EntityNotFoundError('Conversation', str(conversation_id))

        query = (
            select(func.count())
            .select_from(QualConflict)
            .where(QualConflict.ConversationId == conversation_internal_id)
        )
        total_conflicts = (await self.db_session.execute(query)).scalar_one()

        if total_conflicts == 0:
            return None

        query_unresolved = (
            select(func.count())
            .select_from(QualConflict)
            .where(QualConflict.ConversationId == conversation_internal_id, QualConflict.ChosenValue.is_(None))
        )
        unresolved_conflicts = (await self.db_session.execute(query_unresolved)).scalar_one()

        return unresolved_conflicts == 0
