import logging
from uuid import UUI<PERSON>

from sqlalchemy import select
from sqlalchemy.ext.asyncio import AsyncSession

from constants.message import QualFieldName
from exceptions import EntityNotFoundError
from models import QualFieldValue
from repositories.conversation import ConversationRepository


__all__ = ['FieldRepository']
logger = logging.getLogger(__name__)


class FieldRepository:
    """Repository for field-related database operations."""

    def __init__(self, db_session: AsyncSession, conversation_repository: ConversationRepository):
        self.db_session = db_session
        self.conversation_repository = conversation_repository

    def _base_field_value_query(self, conversation_internal_id: int, field_name: QualFieldName):
        return (
            select(QualFieldValue)
            .where(
                QualFieldValue.QualConversationId == conversation_internal_id,
                QualFieldValue.FieldName == field_name,
            )
            .order_by(QualFieldValue.CreatedAt.desc())
        )

    async def _get_internal_id(self, conversation_id: UUID) -> int:
        conversation_internal_id = await self.conversation_repository.get_internal_id(conversation_id)
        if conversation_internal_id is None:
            raise EntityNotFoundError('Conversation', str(conversation_id))
        return conversation_internal_id

    async def get_latest_value(
        self,
        conversation_id: UUID,
        field_name: QualFieldName,
    ) -> QualFieldValue | None:
        """
        Get the latest value of a field for a conversation.
        """
        conversation_internal_id = await self._get_internal_id(conversation_id)
        query = self._base_field_value_query(conversation_internal_id, field_name).limit(1)
        result = await self.db_session.execute(query)
        return result.scalar_one_or_none()

    async def get_previous_value(
        self,
        conversation_id: UUID,
        field_name: QualFieldName,
    ) -> QualFieldValue | None:
        """
        Get the previous value of a field for a conversation.
        """
        conversation_internal_id = await self._get_internal_id(conversation_id)
        query = self._base_field_value_query(conversation_internal_id, field_name).offset(1).limit(1)
        result = await self.db_session.execute(query)
        return result.scalar_one_or_none()

    async def get_recent_field_changes(
        self,
        conversation_id: UUID,
        limit: int = 3,
    ) -> list[QualFieldValue]:
        """
        Get the most recent field changes for a conversation across all fields.

        Args:
            conversation_id: The ID of the conversation
            limit: Maximum number of recent changes to retrieve

        Returns:
            List of recent field changes ordered by creation time (most recent first)
        """
        conversation_internal_id = await self._get_internal_id(conversation_id)
        query = (
            select(QualFieldValue)
            .where(QualFieldValue.QualConversationId == conversation_internal_id)
            .order_by(QualFieldValue.CreatedAt.desc())
            .limit(limit)
        )
        result = await self.db_session.execute(query)
        return list(result.scalars().all())

    async def create(
        self,
        conversation_id: UUID,
        field_name: QualFieldName,
        field_value: str,
        formatted_field_value: str,
    ) -> QualFieldValue:
        """
        Create a new field value in the database.

        Args:
            conversation_id: The ID of the associated conversation
            field_name: The name of the field to update
            field_value: The value to update the field with

        Returns:
            The created field value
        """
        conversation_internal_id = await self._get_internal_id(conversation_id)
        new_field_value = QualFieldValue(
            QualConversationId=conversation_internal_id,
            FieldName=field_name,
            FieldValue=field_value,
            FormattedFieldValue=formatted_field_value,
        )

        self.db_session.add(new_field_value)
        await self.db_session.flush()

        return new_field_value
