import logging
from typing import Any

from config import settings
from core.http_client import CustomAsyncClient
from core.urls import url_join


__all__ = ['EngagementLocationRepository']

logger = logging.getLogger(__name__)


class EngagementLocationRepository:
    """Repository for Engagement Location API operations."""

    def __init__(self, http_client: CustomAsyncClient):
        """
        Initialize the Engagement Location Repository with an HTTP client.

        Args:
            http_client: The CustomAsyncClient to use for requests
        """
        self._http_client = http_client
        self._base_path = str(settings.project_api.base_url)

    async def list(self, token: str) -> list[dict[str, Any]]:
        """
        List all engagement locations.

        Returns:
            list[dict[str, Any]]: A list of engagement locations
        """
        url = url_join(self._base_path, 'project-locations')
        headers = {'Authorization': f'Bearer {token}'} if token else {} if token else {}
        try:
            return (await self._http_client.get(url, headers=headers)).json()

        except Exception as e:
            logger.error('Error listing engagement locations: %s', e)
            raise e
