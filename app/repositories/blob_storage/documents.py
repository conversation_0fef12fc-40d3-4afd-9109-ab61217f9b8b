from typing import Sequence

from .base import BlobStorageRepository


__all__ = ['DocumentBlobStorageRepository']


class DocumentBlobStorageRepository(BlobStorageRepository):
    async def delete_many(self, blob_paths: Sequence[str]) -> None:
        """
        Delete multiple blobs from storage.

        Args:
            blob_paths: List of blob paths to delete
        """
        await self.initialize()

        try:
            async with self._get_service_client() as service_client:
                container_client = service_client.get_container_client(self._container_name)
                deleted_count = 0

                for blob_path in blob_paths:
                    try:
                        # Delete the blob
                        await container_client.delete_blob(blob_path)
                        deleted_count += 1
                        self._logger.info("Deleted blob '%s'", blob_path)
                    except Exception as e:
                        self._logger.error("Failed to delete blob '%s': %s", blob_path, e)
                        # Continue with other blobs even if one fails

                self._logger.info('Successfully deleted %d of %d blobs', deleted_count, len(blob_paths))
        except Exception as e:  # pragma: no cover
            self._logger.error('Failed to initialize blob service for deletion: %s', e)
            raise
