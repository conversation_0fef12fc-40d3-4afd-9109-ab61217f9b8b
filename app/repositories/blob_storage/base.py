import logging

from azure.core.exceptions import ResourceExistsError
from azure.storage.blob import ContentSettings
from azure.storage.blob.aio import BlobServiceClient


__all__ = ['BlobStorageRepository']


class BlobStorageRepository:
    _logger = logging.getLogger(f'{__name__}.{__qualname__}')

    def __init__(self, connection_string: str, container_name: str):
        self._connection_string = connection_string
        self._container_name = container_name
        self._initialized = False

    async def initialize(self) -> None:
        if self._initialized:
            return

        async with self._get_service_client() as service_client:
            container_client = service_client.get_container_client(self._container_name)
            try:
                await container_client.create_container()
                self._logger.info("Container '%s' created", self._container_name)
            except ResourceExistsError:
                pass
            self._initialized = True

    async def upload(self, file_name: str, content: bytes, content_type: str | None = None) -> str:
        """
        Upload a file to blob storage.

        Args:
            file_name: Name of the file
            content: File content as bytes
            content_type: MIME type of the file

        Returns:
            URL of the uploaded blob

        Raises:
            Exception: If upload fails
        """
        await self.initialize()
        try:
            async with self._get_service_client() as service_client:
                container_client = service_client.get_container_client(self._container_name)
                blob_client = container_client.get_blob_client(file_name)
                content_settings = ContentSettings(content_type=content_type) if content_type else None

                await blob_client.upload_blob(
                    content,
                    content_settings=content_settings,
                    overwrite=True,
                )

                self._logger.info("File '%s' uploaded successfully", file_name)
                return blob_client.url

        except Exception as e:  # pragma: no cover
            self._logger.error("Failed to upload file '%s': %s", file_name, e)
            raise

    def _get_service_client(self) -> BlobServiceClient:
        return BlobServiceClient.from_connection_string(self._connection_string)
