import logging
from typing import Any

from config import settings
from core.http_client import CustomAsyncClient


__all__ = ['UserInfoRepository']

logger = logging.getLogger(__name__)


class UserInfoRepository:
    """Repository for User Info API operations."""

    def __init__(self, http_client: CustomAsyncClient):
        """
        Initialize the Service Repository with an HTTP client.

        Args:
            http_client: The CustomAsyncClient to use for requests
        """
        self._http_client = http_client
        self._base_path = str(settings.user_info_api.base_url)

    async def get(self, deloitte_id: str, token: str) -> dict[str, dict[str, Any]]:
        """
        Get user info.

        Returns:
            dict[str, dict[str, Any]]: User info

        Raises:
            Exception: If an error occurs while getting user info
        """
        url = self._base_path
        headers = {'Authorization': f'Bearer {token}'} if token else {}
        params = {'deloiteId': deloitte_id}

        response = await self._http_client.get(url, headers=headers, params=params)
        data = response.json()

        return data
