import logging
from typing import Any

from config import settings
from core.http_client import CustomAsyncClient
from core.urls import url_join


__all__ = ['IndustryRepository']

logger = logging.getLogger(__name__)


class IndustryRepository:
    """Repository for Industry API operations."""

    def __init__(self, http_client: CustomAsyncClient):
        """
        Initialize the Industry Repository with an HTTP client.

        Args:
            http_client: The CustomAsyncClient to use for requests
        """
        self._http_client = http_client
        self._base_path = str(settings.industries_api.base_url)

    async def list(self, token: str) -> list[dict[str, Any]]:
        """
        List all industries.

        Returns:
            list[dict[str, Any]]: A list of industries
        """
        url = url_join(self._base_path, 'industries-all')
        headers = {'Authorization': f'Bearer {token}'} if token else {}
        try:
            return (await self._http_client.get(url, headers=headers)).json()

        except Exception as e:
            logger.error('Error listing industries: %s', e)
            raise e
