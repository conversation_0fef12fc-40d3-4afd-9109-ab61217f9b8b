import logging
import os
from pathlib import Path
from typing import cast

from dotenv import find_dotenv, load_dotenv
from pydantic import HttpUrl

from constants.operation_ids import operation_ids
from schemas.settings import (
    ADSettings,
    AuthSettings,
    BlobStorageSettings,
    CacheSettings,
    DatabaseSettings,
    Environment,
    HTTPClientBackoffSettings,
    HTTPClientSettings,
    IndustriesAPISettings,
    KXDashAPISettings,
    LDMFCountriesAPISettings,
    LoggingSettings,
    OpenAISettings,
    ProjectAPISettings,
    QualsClientsAPISettings,
    QueueSettings,
    RolesAPISettings,
    Settings,
    SignalRSettings,
    UserInfoAPISettings,
)


__all__ = ['settings']


if env_file := os.getenv('ENV_FILE'):
    load_dotenv(dotenv_path=find_dotenv(filename=env_file), override=True)

TRUE_SYNONYMS = {'true', 'yes', '1'}

with open(Path(__file__).parents[1] / 'version') as fp:
    VERSION = fp.read().strip()

ENVIRONMENT = Environment(os.environ['ENVIRONMENT'])


def _get_bool_env(var: str, default: str = 'false', mandatory: bool = False) -> bool:
    return (os.environ[var] if mandatory else os.environ.get(var, default)).lower() in TRUE_SYNONYMS


def _get_test_prefix() -> str:
    return 'test-' if ENVIRONMENT == Environment.TEST else ''


settings = Settings(
    project_name='KX Quals AI Assistant API',
    version=VERSION,
    environment=ENVIRONMENT,
    debug=_get_bool_env('DEBUG'),
    allowed_hosts=tuple(host for x in os.getenv('ALLOWED_HOSTS', '*').split(',') if (host := x.strip())),
    logging=LoggingSettings(
        level=cast(int, getattr(logging, os.environ['LOG_LEVEL'].upper())),
        elevate_severity=_get_bool_env('LOG_ELEVATE_SEVERITY'),
    ),
    auth=AuthSettings(
        ad=ADSettings(
            aad_instance='https://login.microsoftonline.com',
            api_audience=os.environ['AZURE_AD_API_AUDIENCE'],
            aad_tenant_id=os.environ['AZURE_AD_TENANT_ID'],
            required_scopes=frozenset(os.environ['AZURE_AD_REQUIRED_SCOPES'].strip().split(',')),
            blocked_domains=('.cn',),
        ),
        signal_r=SignalRSettings(
            connection_string=os.environ['SIGNAL_R_CONNECTION_STRING'],
            token_lifespan=60,  # minutes
            hub_name='qualMessageHub',
        ),
        auth_free_endpoints=frozenset((operation_ids.root.HEALTH_CHECK, operation_ids.conversation.GET_EXTRA_DATA)),
    ),
    db=DatabaseSettings(
        host=os.environ['DB_HOST'],
        port=os.environ['DB_PORT'],
        user=os.environ['DB_USER'],
        password=os.environ['DB_PASSWORD'],
        name=_get_test_prefix() + os.environ['DB_NAME'],
        driver=os.environ.get('DB_DRIVER', 'ODBC+Driver+17+for+SQL+Server'),
    ),
    blob_storage=BlobStorageSettings(
        connection_string=os.environ['AZURE_STORAGE_CONNECTION_STRING'],
        default_container_name=_get_test_prefix() + os.environ['DEFAULT_BLOB_STORAGE_CONTAINER_NAME'],
        # Document container settings
        document_container_name=_get_test_prefix() + os.environ['DOCUMENT_BLOB_STORAGE_CONTAINER_NAME'],
        max_file_size=250 * 1024 * 1024 + (1024 * 10),  # 250 MB + 10 KB buffer
        max_docs_per_conversation=3,
        max_conversation_size=250 * 1024 * 1024 + (1024 * 10),  # 250 MB + 10 KB buffer
        supported_file_formats={
            # Word documents
            'application/msword',  # .doc
            'application/vnd.openxmlformats-officedocument.wordprocessingml.document',  # .docx
            # PDF documents
            'application/pdf',  # .pdf
            # PowerPoint presentations
            'application/vnd.ms-powerpoint',  # .ppt
            'application/vnd.openxmlformats-officedocument.presentationml.presentation',  # .pptx
        },
    ),
    http_client=HTTPClientSettings(
        timeout=int(os.environ['HTTP_CLIENT_TIMEOUT']),  # seconds
        follow_redirects=_get_bool_env('HTTP_CLIENT_FOLLOW_REDIRECTS', mandatory=True),
        verify_ssl=_get_bool_env('HTTP_CLIENT_VERIFY_SSL', mandatory=True),
        max_connections=int(os.environ['HTTP_CLIENT_MAX_CONNECTIONS']),
        max_keepalive_connections=int(os.environ['HTTP_CLIENT_MAX_KEEPALIVE_CONNECTIONS']),
        backoff=HTTPClientBackoffSettings(
            max_retries=3,
            backoff_base=60,  # seconds
            backoff_factor=0.5,
        ),
    ),
    kx_dash_api=KXDashAPISettings(
        base_url=HttpUrl(os.environ['KX_DASH_API_BASE_URL']),
    ),
    quals_clients_api=QualsClientsAPISettings(
        base_url=HttpUrl(os.environ['QUALS_API_BASE_URL']),
        mock_client_api_enabled=ENVIRONMENT == Environment.TEST or _get_bool_env('MOCK_QUALS_CLIENT_API'),
        mock_client_get_references_list_enabled=_get_bool_env('MOCK_QUALS_CLIENT_GET_REFERENCES_LIST', default='true'),
    ),
    document_queue=QueueSettings(
        connection_string=os.environ['AZURE_QUEUE_CONNECTION_STRING'],
        content_queue_name=os.getenv('AZURE_CONTENT_QUEUE_NAME', 'content-analysis-queue')
        + ('-test' if ENVIRONMENT == Environment.TEST else ''),
    ),
    openai=OpenAISettings(
        endpoint=os.environ['AZURE_OPENAI_ENDPOINT'],
        key=os.environ['AZURE_OPENAI_KEY'],
        deployment=os.getenv('AZURE_OPENAI_DEPLOYMENT', 'gpt-4o'),
        model=os.getenv('AZURE_OPENAI_MODEL', 'gpt-4o'),
        api_version=os.getenv('AZURE_OPENAI_API_VERSION', '2024-08-01-preview'),
        default_temperature=float(os.getenv('AZURE_OPENAI_DEFAULT_TEMPERATURE', 0.0)),
        max_completion_tokens=int(os.getenv('AZURE_OPENAI_MAX_COMPLETION_TOKENS', 1)),
    ),
    cache=CacheSettings(
        default_maxsize=max(1, int(os.getenv('CACHE_DEFAULT_MAXSIZE', 100))),
        default_ttl=max(1, int(os.getenv('CACHE_DEFAULT_TTL', 3600))),  # seconds
        enable_serialization=_get_bool_env('CACHE_ENABLE_SERIALIZATION', default='true'),
        ldmf_countries_maxsize=max(1, int(os.getenv('CACHE_LDMF_COUNTRIES_MAXSIZE', 1))),
        ldmf_countries_ttl=max(1, int(os.getenv('CACHE_LDMF_COUNTRIES_TTL', 3600))),  # seconds
        quals_clients_maxsize=max(1, int(os.getenv('CACHE_QUALS_CLIENTS_MAXSIZE', 2))),
        quals_clients_ttl=max(1, int(os.getenv('CACHE_QUALS_CLIENTS_TTL', 3600))),  # seconds
    ),
    industries_api=IndustriesAPISettings(
        base_url=HttpUrl(os.environ['INDUSTRIES_API_BASE_URL']),
    ),
    project_api=ProjectAPISettings(
        base_url=HttpUrl(os.environ['PROJECT_API_BASE_URL']),
    ),
    roles_api=RolesAPISettings(
        base_url=HttpUrl(os.environ['ROLES_API_BASE_URL']),
    ),
    user_info_api=UserInfoAPISettings(
        base_url=HttpUrl(os.environ['USER_INFO_API_BASE_URL']),
    ),
    ldmf_countries_api=LDMFCountriesAPISettings(
        base_url=HttpUrl(os.environ['LDMF_COUNTRIES_BASE_URL']),
    ),
    append_collected_data_to_message_response=_get_bool_env(
        'APPEND_COLLECTED_DATA_TO_MESSAGE_RESPONSE', default='true'
    ),
    support_url=HttpUrl(os.environ['SUPPORT_URL']),
)
