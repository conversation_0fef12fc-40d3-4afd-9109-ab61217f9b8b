import logging
import sys

from .app_settings import Settings


__all__ = ['configure_logging']


class InfoToWarningElevationFilter(logging.Filter):
    """
    A logging filter that elevates all INFO level messages to WARNING.
    """

    def __init__(self, *, settings: Settings):
        super().__init__()
        self._app_settings = settings

    def filter(self, record):
        if self._app_settings.logging.elevate_severity and record.levelno == logging.INFO:
            record.levelno = logging.WARNING
            record.levelname = logging.getLevelName(logging.WARNING)
        return True  # True allows the record to be processed further


def configure_logging(settings: Settings):
    logging.basicConfig(
        level=settings.logging.level,
        stream=sys.stdout,
    )

    # root logger
    root_logger = logging.getLogger()
    for handler in root_logger.handlers:
        handler.addFilter(InfoToWarningElevationFilter(settings=settings))

    # uvicorn loggers
    for logger_name in ('uvicorn', 'uvicorn.access', 'uvicorn.error'):
        uvicorn_logger = logging.getLogger(logger_name)
        for handler in uvicorn_logger.handlers:
            handler.addFilter(InfoToWarningElevationFilter(settings=settings))
