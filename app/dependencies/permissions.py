from uuid import UUID

from fastapi import Depends, Request

from constants.operation_ids import operation_ids
from exceptions import EntityNotFoundError

from .services import ConversationMessageServiceDep, ConversationServiceDep


__all__ = ['OwnerOnlyPermissionDep']


class OwnerOnlyPermission:
    _MESSAGE_IN_PATH_OPERATION_IDS = {
        operation_ids.message.GET,
    }
    _CONVERSATION_IN_PATH_OPERATION_IDS = {
        operation_ids.conversation.GET,
        operation_ids.conversation.DELETE,
        operation_ids.message.LIST,
        operation_ids.message.GET_LAST,
        operation_ids.extracted_data_summary.GET,
        operation_ids.conversation.UPDATE_QUAL_ID,
        operation_ids.conversation.ENGAGEMENT_CHAT_CREATE,
    }
    _CONVERSATION_IN_QUERY_OPERATION_IDS = {
        operation_ids.auth.CREATE_SIGNAL_R_JWT,
    }
    _QUAL_IN_PATH_OPERATION_IDS = {
        operation_ids.extracted_data_summary.GET_BY_QUAL_ID,
        operation_ids.conversation.CONVERT_QUAL_ID_TO_CONV_ID,
    }

    @staticmethod
    def _convert_to_uuid(raw_entity_id: str, entity_type_name: str) -> UUID:
        """Convert string to UUID with proper error handling."""
        try:
            return UUID(raw_entity_id)
        except ValueError:
            raise EntityNotFoundError(entity_type_name, raw_entity_id)

    def _extract_parameter(self, request: Request, param_name: str, from_query: bool = False) -> str:
        """Extract parameter from request path or query parameters."""
        if from_query:
            return request.query_params[param_name]
        return request.path_params[param_name]

    async def _get_owner_id_for_uuid_operation(
        self,
        request: Request,
        param_name: str,
        entity_type_name: str,
        service_method,
        from_query: bool = False,
    ) -> UUID | None:
        """Handle UUID-based operations with common conversion and error handling."""
        raw_entity_id = self._extract_parameter(request, param_name, from_query)
        entity_id = self._convert_to_uuid(raw_entity_id, entity_type_name)
        return await service_method(entity_id)

    async def __call__(
        self,
        request: Request,
        conversation_service: ConversationServiceDep,
        message_service: ConversationMessageServiceDep,
    ):
        route = request.scope.get('route')
        operation_id = route.operation_id if route else None

        if operation_id in self._MESSAGE_IN_PATH_OPERATION_IDS:
            entity_type_name = 'Message'
            owner_id = await self._get_owner_id_for_uuid_operation(
                request, 'message_id', entity_type_name, message_service.get_owner_id
            )
            entity_id = self._extract_parameter(request, 'message_id')

        elif operation_id in self._CONVERSATION_IN_PATH_OPERATION_IDS:
            entity_type_name = 'Conversation'
            owner_id = await self._get_owner_id_for_uuid_operation(
                request, 'conversation_id', entity_type_name, conversation_service.get_owner_id
            )
            entity_id = self._extract_parameter(request, 'conversation_id')

        elif operation_id in self._CONVERSATION_IN_QUERY_OPERATION_IDS:
            entity_type_name = 'Conversation'
            owner_id = await self._get_owner_id_for_uuid_operation(
                request, 'conversation_id', entity_type_name, conversation_service.get_owner_id, from_query=True
            )
            entity_id = self._extract_parameter(request, 'conversation_id', from_query=True)

        elif operation_id in self._QUAL_IN_PATH_OPERATION_IDS:
            entity_type_name = 'Conversation'
            # Keep as string for qual_id operations
            entity_id = self._extract_parameter(request, 'qual_id')
            owner_id = await conversation_service.get_owner_id_by_qual_id(entity_id)

        else:
            raise NotImplementedError(f'"Owner only" permission check not yet supported for operation "{operation_id}"')

        if owner_id != request.state.user.id:
            raise EntityNotFoundError(entity_type_name, entity_id)


OwnerOnlyPermissionDep = Depends(OwnerOnlyPermission())
