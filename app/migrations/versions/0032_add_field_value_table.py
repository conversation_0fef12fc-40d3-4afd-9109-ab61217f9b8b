"""Add Field Value table

Revision ID: 0032
Revises: 0031
Create Date: 2025-08-13 23:44:40.967500

"""

from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = '0032'
down_revision: Union[str, None] = '0031'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_table(
        'QualFieldValue',
        sa.Column('Id', sa.Integer(), autoincrement=True, nullable=False),
        sa.Column('QualConversationId', sa.Integer(), nullable=False),
        sa.Column(
            'FieldName',
            sa.Enum(
                'business_issues',
                'scope_approach',
                'value_delivered',
                'engagement_summary',
                'one_line_description',
                name='qualfieldname',
            ),
            nullable=False,
        ),
        sa.Column('FieldValue', sa.UnicodeText(), nullable=False),
        sa.Column('FormattedFieldValue', sa.UnicodeText(), nullable=False),
        sa.Column('CreatedAt', sa.DateTime(), server_default=sa.text('CURRENT_TIMESTAMP'), nullable=False),
        sa.ForeignKeyConstraint(
            ['QualConversationId'],
            ['QualConversation.Id'],
        ),
        sa.PrimaryKeyConstraint('Id'),
    )
    op.add_column('QualConversationMessage', sa.Column('Command', sa.UnicodeText(), nullable=True))
    op.add_column('QualConversationMessage', sa.Column('QualFields', sa.UnicodeText(), nullable=True))
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_column('QualConversationMessage', 'QualFields')
    op.drop_column('QualConversationMessage', 'Command')
    op.drop_table('QualFieldValue')
    # ### end Alembic commands ###
