"""add StateContext column to conversation

Revision ID: 0038
Revises: 0036
Create Date: 2025-09-03 14:04:30.521189

"""

from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = '0038'
down_revision: Union[str, None] = '0037'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column(
        'QualConversation', sa.Column('StateContext', sa.JSON(), server_default=sa.text("'{}'"), nullable=True)
    )
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    (
        op.execute(
            """
            DECLARE @df_name sysname;

            SELECT @df_name = name
            FROM sys.default_constraints
            WHERE parent_object_id = OBJECT_ID('QualConversation')
            AND parent_column_id = (
                    SELECT column_id
                    FROM sys.columns
                    WHERE object_id = OBJECT_ID('QualConversation')
                    AND name = 'StateContext'
                );
            IF @df_name IS NOT NULL
                EXEC('ALTER TABLE [QualConversation] DROP CONSTRAINT [' + @df_name + ']');
        """
        ),
    )

    op.drop_column('QualConversation', 'StateContext')
    # ### end Alembic commands ###
