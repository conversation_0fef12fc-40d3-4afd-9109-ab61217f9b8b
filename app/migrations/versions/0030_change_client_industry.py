"""change client_industries

Revision ID: 0030
Revises: 0029
Create Date: 2025-08-05 13:41:34.867846

"""

from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = '0030'
down_revision: Union[str, None] = '0029'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.alter_column(
        'QualExtractedData',
        'ClientIndustry',
        existing_type=sa.VARCHAR(collation='SQL_Latin1_General_CP1_CI_AS'),
        type_=sa.JSON(),
        nullable=True,
        new_column_name='ClientIndustries',
    )
    op.drop_column('QualExtractedData', 'Industries')
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.alter_column(
        'QualExtractedData',
        'ClientIndustries',
        existing_type=sa.JSON(),
        type_=sa.VARCHAR(collation='SQL_Latin1_General_CP1_CI_AS'),
        nullable=True,
        new_column_name='ClientIndustry',
    )
    op.add_column('QualExtractedData', sa.Column('Industries', sa.UnicodeText(), nullable=True))
    # ### end Alembic commands ###
