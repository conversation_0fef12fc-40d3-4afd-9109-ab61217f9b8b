"""change client_services

Revision ID: 0033
Revises: 0032
Create Date: 2025-08-12 11:46:18.000000

"""

from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = '0033'
down_revision: Union[str, None] = '0032'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.alter_column(
        'QualExtractedData',
        'ClientServices',
        existing_type=sa.VARCHAR(collation='SQL_Latin1_General_CP1_CI_AS'),
        type_=sa.JSON(),
        nullable=True,
    )
    op.drop_column('QualExtractedData', 'Services')
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.alter_column(
        'QualExtractedData',
        'ClientServices',
        existing_type=sa.JSON(),
        type_=sa.VARCHAR(collation='SQL_Latin1_General_CP1_CI_AS'),
        nullable=True,
    )
    op.add_column('QualExtractedData', sa.Column('Services', sa.UnicodeText(), nullable=True))
    # ### end Alembic commands ###
