"""extend extract table

Revision ID: 0029
Revises: 0028
Create Date: 2025-08-08 15:29:15.791412

"""

from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = '0029'
down_revision: Union[str, None] = '0028'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('QualExtractedData', sa.Column('EngagementFee', sa.Float(), nullable=True))
    op.add_column('QualExtractedData', sa.Column('EngagementFeeCurrency', sa.String(length=3), nullable=True))
    op.add_column('QualExtractedData', sa.Column('IsEngagementFeeUnknown', sa.<PERSON>(), nullable=True))
    op.add_column('QualExtractedData', sa.<PERSON>umn('IsProBono', sa.<PERSON>(), nullable=True))
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_column('QualExtractedData', 'IsProBono')
    op.drop_column('QualExtractedData', 'IsEngagementFeeUnknown')
    op.drop_column('QualExtractedData', 'EngagementFeeCurrency')
    op.drop_column('QualExtractedData', 'EngagementFee')
    # ### end Alembic commands ###
