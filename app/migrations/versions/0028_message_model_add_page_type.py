"""message model add page_type

Revision ID: 0028
Revises: 0027
Create Date: 2025-08-02 15:57:57.265632

"""

from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa
from sqlalchemy.sql import text  # Import the 'text' function


# revision identifiers, used by Alembic.
revision: str = '0028'
down_revision: Union[str, None] = '0027'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def get_constraint_name(table_name, column_name):
    """
    Dynamically finds the name of a default constraint for a given column.
    This function is specific to SQL Server's system views.
    """
    conn = op.get_bind()
    query = text("""
        SELECT
            dc.name
        FROM
            sys.default_constraints dc
            JOIN sys.columns c ON dc.parent_object_id = c.object_id AND dc.parent_column_id = c.column_id
            JOIN sys.tables t ON dc.parent_object_id = t.object_id
        WHERE
            t.name = :table_name
            AND c.name = :column_name;
    """)
    result = conn.execute(query, {'table_name': table_name, 'column_name': column_name})
    constraint_name = result.scalar()
    return constraint_name


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column(
        'QualConversationMessage',
        sa.Column(
            'PageType',
            sa.Enum('prompt', 'engagement_description', name='pagetype'),
            server_default=sa.text("'prompt'"),
            nullable=False,
        ),
    )
    op.add_column(
        'QualConversationMessage',
        sa.Column(
            'SuggestionsQual',
            sa.UnicodeText(),
            nullable=False,
            server_default='[]',
        ),
    )
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    table_name = 'QualConversationMessage'
    column_name = 'SuggestionsQual'

    # Step 1: Find the dynamically named default constraint
    constraint_name = get_constraint_name(table_name, column_name)

    # Step 2: Drop the constraint if it exists
    if constraint_name:
        op.execute(f'ALTER TABLE {table_name} DROP CONSTRAINT {constraint_name};')

    # Step 3: Now it's safe to drop the column
    op.drop_column(table_name, column_name)

    column_name = 'PageType'
    constraint_name = get_constraint_name(table_name, column_name)
    if constraint_name:
        op.execute(f'ALTER TABLE {table_name} DROP CONSTRAINT {constraint_name};')

    op.drop_column(table_name, column_name)
    # ### end Alembic commands ###
