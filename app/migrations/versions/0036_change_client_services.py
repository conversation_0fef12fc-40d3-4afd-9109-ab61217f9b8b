"""change engagement_locations

Revision ID: 0036
Revises: 0035
Create Date: 2025-08-26 11:08:12.000000

"""

from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = '0036'
down_revision: Union[str, None] = '0035'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.alter_column(
        'QualExtractedData',
        'EngagementLocations',
        existing_type=sa.VARCHAR(collation='SQL_Latin1_General_CP1_CI_AS'),
        type_=sa.JSON(),
        nullable=True,
    )
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.alter_column(
        'QualExtractedData',
        'EngagementLocations',
        existing_type=sa.JSON(),
        type_=sa.VARCHAR(collation='SQL_Latin1_General_CP1_CI_AS'),
        nullable=True,
    )
    # ### end Alembic commands ###
