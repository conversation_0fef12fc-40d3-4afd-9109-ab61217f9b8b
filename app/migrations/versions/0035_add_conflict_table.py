"""add_conflict_table

Revision ID: 0035
Revises: 0034
Create Date: 2025-08-15 17:56:55.772752

"""

from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = '0035'
down_revision: Union[str, None] = '0034'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_table(
        'QualConflict',
        sa.<PERSON>umn('Id', sa.Integer(), autoincrement=True, nullable=False),
        sa.Column('ConversationId', sa.Integer(), nullable=False),
        sa.Column('Description', sa.UnicodeText(), nullable=False),
        sa.<PERSON>umn('Field', sa.Enum('objective_and_scope', 'outcomes', name='conflictfield'), nullable=False),
        sa.<PERSON>umn('ConflictingValues', sa.JSON(), nullable=False),
        sa.Column('ChosenValue', sa.UnicodeText(), nullable=True),
        sa.ForeignKeyConstraint(
            ['ConversationId'],
            ['QualConversation.Id'],
        ),
        sa.PrimaryKeyConstraint('Id'),
    )
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_table('QualConflict')
    # ### end Alembic commands ###
